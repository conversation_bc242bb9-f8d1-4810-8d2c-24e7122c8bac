# Task ID: 23
# Title: 订单中心列表页
# Status: pending
# Dependencies: 3, 4
# Priority: high
# Description: 开发订单中心列表页面，页面顶部采用Tab栏进行订单状态分类（全部、待付款、待服务、待评价、退款/售后）。列表中每个订单以卡片形式展示关键信息和操作按钮。
# Details:
使用`u-tabs`作为顶部Tab导航，每个Tab对应一个订单列表。订单卡片使用自定义组件，根据订单状态动态显示操作按钮（如“去支付”、“去评价”）。

# Test Strategy:
测试Tab切换功能是否正常，订单列表是否根据状态正确筛选；验证订单卡片信息展示是否完整，操作按钮是否根据状态动态变化。

# Subtasks:
## 1. Define Order Data Models & API Integration [pending]
### Dependencies: None
### Description: Establish the data structure for order objects (e.g., ID, status, items, total, dates) and integrate with the backend API to fetch order lists. This includes defining endpoints for different order statuses or a single endpoint with status filtering.
### Details:
This subtask focuses on the backend communication and data contract.

## 2. Develop Tabbed Navigation UI Component [pending]
### Dependencies: None
### Description: Create the reusable UI component for the tabbed navigation (e.g., 'All Orders', 'Pending', 'Shipped', 'Delivered'). This component will manage tab selection state and provide visual feedback.
### Details:
Focus on the visual and interactive aspects of the tabs, without connecting to data yet.

## 3. Implement Base Order Card Display Component [pending]
### Dependencies: None
### Description: Develop the core UI component for displaying a single order's details (e.g., order ID, date, total, basic item list). This component should be reusable and accept order data as props.
### Details:
This subtask focuses on the static layout and display of an individual order.

## 4. Add Dynamic Action Buttons & Status-Based Rendering to Order Card [pending]
### Dependencies: 23.3
### Description: Enhance the Order Card component (from Subtask 3) to conditionally render action buttons (e.g., 'Cancel', 'Track', 'Receive', 'Reorder') and display status-specific information based on the order's current status.
### Details:
This subtask adds the dynamic and interactive elements to the order card based on its status.

## 5. Integrate Data Fetching with Tabs and Order Cards [pending]
### Dependencies: 23.1, 23.2, 23.4
### Description: Connect the data fetching logic (from Subtask 1) to the tabbed navigation (from Subtask 2) and the Order Card display (from Subtask 4). Implement the logic for switching tabs, triggering appropriate data fetches/filters, and populating the list of Order Cards.
### Details:
This subtask brings all components together, managing state and data flow for the entire page.

