# Task ID: 19
# Title: “单点厨师”模式厨师详情与预约
# Status: pending
# Dependencies: 18, 10, 11, 13
# Priority: high
# Description: 开发“单点厨师”模式的厨师详情页面，包含招牌套餐、个人主页、用户评价、可约时间等Tab式内容区。实现两种预约路径：预约厨师基础服务和预订厨师个人套餐，并接入通用预约流程。
# Details:
厨师详情页使用`u-tabs`切换内容。招牌套餐Tab展示厨师个人套餐列表。可约时间Tab使用日历组件展示厨师档期。预约按钮点击后，根据路径跳转到通用预约页，并带入厨师或套餐信息。

# Test Strategy:
测试厨师详情页各Tab内容切换是否正常；验证两种预约路径是否能正确跳转到预约页，并带入相应数据；检查厨师可约时间日历显示是否准确。

# Subtasks:
## 1. Chef Detail Page Core Structure [pending]
### Dependencies: None
### Description: Set up the foundational layout and shell for the 'à la carte chef' detail page, including header, main content area, and basic styling.
### Details:
This involves creating the main HTML/component structure and initial CSS for the chef's dedicated page.

## 2. Multi-Tab Navigation Implementation [pending]
### Dependencies: 19.1
### Description: Develop the interactive tab navigation system for the chef detail page, including tabs for 'Signature Packages', 'Profile', 'Reviews', and 'Availability'.
### Details:
Implement the UI/UX for switching between tabs and ensure smooth transitions. This is the container for the different content sections.

## 3. Profile & Signature Packages Tab Content Development [pending]
### Dependencies: 19.2
### Description: Populate the 'Profile' tab with chef's personal information, bio, and general service descriptions, and the 'Signature Packages' tab with details of pre-defined culinary offerings, pricing, and descriptions.
### Details:
Integrate data models for chef profiles and signature packages. Design the display of this information within their respective tabs.

## 4. Reviews Tab Development [pending]
### Dependencies: 19.2
### Description: Implement the 'Reviews' tab functionality, allowing display of customer feedback, ratings, and potentially a mechanism for users to submit new reviews.
### Details:
Connect to the review data source, design the review display components (e.g., star ratings, comments, user avatars).

## 5. Availability Tab & Calendar Integration [pending]
### Dependencies: 19.2
### Description: Develop the 'Availability' tab, integrating a calendar component that allows chefs to manage their available dates/times and users to view open slots for booking.
### Details:
Implement a robust calendar UI, integrate with backend availability data, and ensure real-time updates for booking slots.

## 6. Implement Distinct Booking Paths [pending]
### Dependencies: 19.3, 19.5
### Description: Create two separate booking initiation flows: one for a basic service request (e.g., custom inquiry) and another for selecting a specific signature package.
### Details:
Design the UI/UX for starting a booking from either a general inquiry button or a 'Book Package' button on a specific package. These paths will collect initial booking parameters.

## 7. Universal Booking Process Integration [pending]
### Dependencies: 19.6
### Description: Connect both distinct booking paths (basic service and signature package) to the existing universal booking process, ensuring all necessary data (chef ID, service type, package ID, selected date/time) is passed correctly.
### Details:
Map the data collected from the two distinct paths to the input requirements of the universal booking flow, ensuring a seamless transition for the user.

