# Task ID: 28
# Title: 推广员中心页面
# Status: pending
# Dependencies: 26, 13
# Priority: medium
# Description: 开发推广员中心页面，该页面在用户成功申请推广员后可见。页面显著位置提供专属推广码和一键复制推广链接。展示累计邀请人数、累计订单数、累计佣金和可提现佣金，并接入提现流程。
# Details:
推广员中心页面展示推广数据看板。推广码和链接可使用`u-copy`组件实现一键复制。提现按钮点击后，进入提现流程（复用厨师端的提现逻辑）。

# Test Strategy:
测试推广员中心页面数据展示是否准确；验证推广码和链接的复制功能；检查提现流程是否正常。

# Subtasks:
## 1. Develop Promoter Data Display [pending]
### Dependencies: None
### Description: Implement the user interface and backend integration to fetch and display core promotion data, including total invites, successful orders, and earned commission, on the promoter center page.
### Details:
Design UI components for data display. Integrate with backend API endpoints (e.g., /api/promoter/stats) to retrieve invite, order, and commission figures. Ensure data is presented clearly.

## 2. Implement Referral Link Copy Feature [pending]
### Dependencies: 28.1
### Description: Add functionality to the promoter center page allowing users to easily copy their unique referral link to the clipboard with a single click.
### Details:
Create a 'Copy Link' button or icon. Utilize JavaScript's `navigator.clipboard.writeText()` API. Provide visual feedback (e.g., 'Copied!') upon successful copy.

## 3. Develop Withdrawal Request Interface [pending]
### Dependencies: 28.1
### Description: Create the user interface for initiating a withdrawal request, including input fields for the withdrawal amount and any necessary confirmation steps. Implement client-side validation.
### Details:
Design a form or modal for withdrawal requests. Include an input field for the amount. Implement client-side validation to ensure the amount is valid (e.g., positive, within available balance).

## 4. Integrate Withdrawal API Endpoint [pending]
### Dependencies: 28.3
### Description: Connect the withdrawal request UI to the backend API endpoint responsible for processing withdrawal requests, handling success/failure responses, and updating the user's commission balance.
### Details:
Integrate with the backend withdrawal API (e.g., POST /api/promoter/withdraw). Implement error handling for API responses. Update the displayed commission balance after a successful withdrawal.

