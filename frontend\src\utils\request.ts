// 基础配置
const BASE_URL = ''; // 替换为你的 API 地址
const TIME_OUT = 10000;

// 请求封装
const request = (options: UniApp.RequestOptions) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data,
      timeout: TIME_OUT,
      header: {
        ...options.header,
        // 在这里添加固定的请求头，例如 token
        'Authorization': uni.getStorageSync('token') || ''
      },
      success: (res) => {
        // 在这里可以进行响应拦截
        if (res.statusCode === 200) {
          // 成功响应
          resolve(res.data);
        } else {
          // 错误处理
          uni.showToast({
            title: '请求失败',
            icon: 'none'
          });
          reject(res);
        }
      },
      fail: (err) => {
        // 网络错误等
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

export default request; 