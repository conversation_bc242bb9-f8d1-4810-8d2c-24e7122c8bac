# Task ID: 36
# Title: 厨师接单状态与服务时间管理
# Status: pending
# Dependencies: 29
# Priority: high
# Description: 在厨师端“我的”主页顶部提供一个醒目的全局开关，允许厨师在“接单中”和“休息中”两个状态之间实时切换。同时，开发一个全屏日历视图，供厨师管理未来特定日期或时间段的可预约状态。
# Details:
全局开关使用`u-switch`组件。服务时间管理使用自定义日历组件，厨师可点击日期切换状态。这些状态将同步到用户端，影响厨师的可见性和可预约性。

# Test Strategy:
测试接单状态切换功能是否正常，并验证用户端厨师列表的显示变化；检查服务时间日历的设置和显示是否准确，是否能正确锁定厨师档期。

# Subtasks:
## 1. Implement Global On/Off Duty Toggle [pending]
### Dependencies: None
### Description: Develop the user interface and frontend logic for a global switch that allows a chef to set their overall availability status (on-duty/off-duty). This toggle should control the chef's general visibility.
### Details:
This includes UI component development, state management for the toggle, and initial frontend event handling.

## 2. Develop Service Time Calendar View [pending]
### Dependencies: 36.1
### Description: Create an interactive calendar interface where chefs can visualize and manage their specific service time slots. This view should reflect the global on/off duty status.
### Details:
Focus on calendar rendering, navigation (month/week view), and basic display of available/unavailable days.

## 3. Implement Date/Time Slot Selection Logic [pending]
### Dependencies: 36.2
### Description: Develop the frontend logic for selecting, adding, editing, and deleting specific date and time slots within the service time calendar view. This includes handling overlapping slots and validation.
### Details:
This involves modal/form for slot creation/editing, validation rules, and local state updates for selected slots.

## 4. Design and Implement Backend API for Availability Management [pending]
### Dependencies: None
### Description: Create robust backend API endpoints to store, retrieve, update, and delete chef availability data, including the global on/off status and specific service time slots.
### Details:
Define data models for chef availability, implement CRUD operations, and ensure secure access to endpoints.

## 5. Implement Frontend-Backend Synchronization [pending]
### Dependencies: 36.1, 36.3, 36.4
### Description: Establish real-time or near real-time synchronization between the frontend UI (global toggle, calendar, time slots) and the backend API to ensure availability data is consistent, persistent, and accurately reflected for users.
### Details:
Implement API calls from frontend components, handle responses, error handling, and update UI based on backend data. Consider optimistic updates or loading states.

