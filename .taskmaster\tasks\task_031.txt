# Task ID: 31
# Title: 厨师个人资料管理页面
# Status: pending
# Dependencies: 30
# Priority: high
# Description: 开发厨师个人资料管理页面，采用多Tab页结构，包括基本信息（形象照、昵称、格言）、职业履历（厨龄、工作履历、健康证、资质证书、荣誉）、服务信息（擅长菜系、基础服务费、增值服务选择、菜品图集）。
# Details:
使用`u-tabs`作为页面导航。每个Tab页包含对应的表单组件，如`u-upload`用于图片上传，`u-input`、`u-select`、`u-checkbox-group`等用于信息填写。资料提交后需等待后台审核。

# Test Strategy:
测试各Tab页的切换和表单填写功能是否正常；验证图片上传和预览功能；检查必填项校验和提交审核流程。

# Subtasks:
## 1. Design and Implement Multi-Tab UI Structure [pending]
### Dependencies: None
### Description: Create the foundational multi-tab layout for the chef's personal profile management page, ensuring smooth navigation between 'Basic Info', 'Professional Resume', and 'Service Info' tabs.
### Details:
Define tab components, styling, and state management for tab selection. Ensure responsiveness.

## 2. Develop Basic Information Form [pending]
### Dependencies: 31.1
### Description: Build the form for the 'Basic Info' tab, including fields for personal details like name, contact information, and a profile picture placeholder.
### Details:
Implement input fields for text, email, phone. Include validation rules. Prepare for profile image upload integration.

## 3. Develop Professional Resume Form [pending]
### Dependencies: 31.1
### Description: Create the form for the 'Professional Resume' tab, allowing chefs to input their certifications, awards, honors, and work experience.
### Details:
Design dynamic fields for adding multiple certifications/honors. Include date pickers and text areas for descriptions. Implement validation.

## 4. Develop Service Information Form [pending]
### Dependencies: 31.1
### Description: Construct the form for the 'Service Info' tab, covering cuisine types, pricing/fees, availability, and a gallery section for showcasing dishes.
### Details:
Implement multi-select for cuisine types, numerical inputs for fees, and a dedicated section for image uploads for the gallery.

## 5. Integrate Image Upload Functionality [pending]
### Dependencies: 31.2, 31.4
### Description: Implement robust image upload capabilities for the profile picture (Basic Info) and the service gallery (Service Info), including preview and deletion options.
### Details:
Develop front-end image selection and preview. Integrate with a backend storage solution (e.g., cloud storage). Handle file type and size validation.

## 6. Implement Form Submission and Backend API Integration [pending]
### Dependencies: 31.2, 31.3, 31.4, 31.5
### Description: Develop the logic for submitting data from all forms across the tabs to the backend API, including data validation and error handling.
### Details:
Create API endpoints for profile data submission. Implement data serialization and deserialization. Handle success/error responses and user feedback.

