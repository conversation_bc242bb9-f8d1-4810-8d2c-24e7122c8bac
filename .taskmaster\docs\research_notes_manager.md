### **后端管理系统功能需求推导**

#### **引言**

本文档基于对 `research_notes.md` 的深入分析，并结合 `后端基础需求.md` 中定义的角色权限，旨在推导出支撑我们整个"上门厨师"平台所需的核心后端管理功能。

**访问方式**:
- **网页端 (PC)**: 提供功能最全面的管理后台，适用于运营、财务、系统管理等需要复杂操作和数据分析的角色。
- **小程序端**: 提供核心功能的移动版本，方便调度员等角色随时随地处理紧急任务，如订单跟进、厨师沟通、即时审核等。

---

#### **一、 厨师管理模块 (Chef Management)**

这是后台系统的核心，负责管理平台最重要的资源——厨师。

1.  **厨师列表与查询**:
    *   **功能描述**: 提供一个所有厨师的列表，支持多维度搜索和筛选。
    *   **筛选条件**: 状态（待审核/已激活/已暂停/已解约）、姓名、手机号、接单区域、厨师等级、菜系。
    *   **负责人**:
        *   `[调度员]`: 查看和搜索厨师，以便进行订单分配和日常沟通。
        *   `[运营管理]`: 查看和筛选，用于分析厨师结构和区域分布。
        *   `[系统管理]`: 拥有全部查看权限。

2.  **厨师入驻审核流程 (Onboarding Approval)**:
    *   **功能描述**: 一个结构化的审核工作流，用于处理新厨师的入驻申请。系统需要一个审核队列。
    *   **审核步骤**:
        *   **基本信息审核**: 审核`形象照`、`格言`等是否合规。
        *   **职业履历审核**: 审核`健康证`（有效期）、`执业资格证`、`荣誉`等证件图片的真实性与清晰度。审核员需能放大查看图片。
        *   **服务信息审核**: 审核`菜系`选择是否合理，`菜品图`是否符合要求。
        *   **实名认证状态同步**: 自动接收并展示来自第三方eID服务的认证结果（成功/失败），此项无需人工审核，但状态必须清晰可见。
    *   **操作**: 审核员可以对每个需要审核的模块执行"通过"或"驳回"（并填写驳回理由）的操作。驳回后，厨师端应收到通知。
    *   **负责人**:
        *   `[调度员]`: 负责执行大部分审核工作，是质量控制的第一道防线。
        *   `[运营管理]`: 可复核或处理疑难审核案例。

3.  **厨师详情与编辑**:
    *   **功能描述**: 查看厨师的所有信息（与厨师端"我的资料"完全对应），并支持修改。
    *   **可编辑项**: 几乎所有厨师自己填写的信息都应支持后台修改，以备厨师无法自行操作时的人工支持。
    *   **特殊操作**:
        *   **账号状态管理**: 手动`暂停`或`激活`厨师账号。
        *   **重置密码**: 为厨师重置登录密码。
        *   **修改服务费/等级**: `运营管理`可根据政策调整厨师的服务费标准或等级。
    *   **负责人**:
        *   `[调度员]`: 处理日常的信息修改请求。
        *   `[运营管理]`: 修改厨师等级、服务费等核心运营数据。
        *   `[系统管理]`: 可执行所有修改，包括冻结/删除等高危操作。

4.  **接单区域更换申请管理**:
    *   **功能描述**: 一个申请队列，展示所有厨师提交的区域更换申请。
    *   **操作**: 运营人员可以`批准`或`驳回`申请。
    *   **负责人**: `[运营管理]`，因为这直接关系到平台的区域供需平衡。

---

#### **二、 订单管理模块 (Order Management)**

负责处理所有交易的核心流程。

1.  **订单列表与查询**:
    *   **功能描述**: 一个包含所有订单的综合列表。
    *   **筛选条件**: 订单状态（待接单/待服务/服务中/已完成/已评价/已退款/已取消）、订单号、用户手机号、厨师姓名、服务时间、服务模式（一口价/单点厨师等）。
    *   **负责人**: 所有角色均可查看，但权限范围不同。`[财务对账]`尤其关注已完成和已退款的订单。

2.  **订单详情页**:
    *   **功能描述**: 展示订单的全部细节，包括服务信息、用户与厨师信息、时间轴（下单、接单、开始服务、完成）、账单明细、增值服务、用户备注等。
    *   **负责人**: 所有角色均可查看。

3.  **手动派单/改派**:
    *   **功能描述**: 对于"一口价"、"精选套餐"等需要平台派单的模式，当自动派单失败或需要人工干预时，调度员可以选择一个符合条件的厨师，手动将订单指派给他。也支持在特殊情况下进行订单改派。
    *   **负责人**: `[调度员]`。

4.  **退款/售后处理**:
    *   **功能描述**: 管理用户提交的退款或售后申请。
    *   **流程**: 调度员跟进、核实情况 -> 提交退款建议 -> 财务执行退款。
    *   **负责人**:
        *   `[调度员]`: 负责跟进和初步判断。
        *   `[财务对账]`: 负责执行退款操作，并记录流水。

5.  **"主题咨询"线索管理**:
    *   **功能描述**: 一个独立的销售线索列表，专门管理来自"私人定制"的咨询请求。
    *   **状态管理**: 线索状态应包含：新线索 -> 跟进中 -> 已联系 -> 已转化/已关闭。
    *   **负责人**: `[调度员]` 或专门的销售团队。

---

#### **三、 内容与产品管理模块 (Content & Product Management)**

负责管理用户端可见的所有服务产品和内容。

1.  **套餐管理**:
    *   **功能描述**: 管理平台上的所有服务套餐。
    *   **分类**:
        *   **平台精选套餐**: `运营管理`可以创建、编辑、上下架平台官方的精选套餐。
        *   **厨师个人套餐审核**: 厨师在自己后台创建的套餐，需要经过`运营管理`审核后才能在前端展示。后台需要一个审核队列。
    *   **负责人**: `[运营管理]`。

2.  **服务模式管理**:
    *   **功能描述**: 配置不同服务模式（一口价、私人定制、主题宴席等）的参数。
    *   **可配置项**: 基础价格、菜品数量、人均单价、超时附加费、节假日附加费等。
    *   **负责人**: `[运营管理]`。

3.  **首页内容配置**:
    *   **功能描述**: 灵活配置用户端首页的展示内容。
    *   **可配置项**: 轮播广告图（上传、链接、排序）、核心功能入口（图标、名称、链接、排序）、推荐的"明星厨师"列表（手动选择、排序）、推荐的"热门套餐"列表。
    *   **负责人**: `[运营管理]`。

4.  **评价管理**:
    *   **功能描述**: 查看所有用户提交的评价，并进行管理。
    *   **操作**: 可以隐藏恶意或不合规的评价。
    *   **负责人**: `[调度员]`、`[运营管理]`。

---

#### **四、 财务管理模块 (Financial Management)**

负责处理所有与资金相关的业务。

1.  **提现申请管理**:
    *   **功能描述**: 一个专门的队列，展示所有厨师提交的提现申请。
    *   **信息展示**: 申请人、申请金额、银行账户信息、申请时间。
    *   **操作**: 财务人员在完成线下转账后，在系统中将申请标记为`已处理`或`失败`。
    *   **负责人**: `[财务对账]`。

2.  **账单与流水**:
    *   **功能描述**: 提供平台所有资金流水的详细记录，包括用户支付、退款、厨师提现、平台抽成等。
    *   **负责人**: `[财务对账]`。

3.  **财务报表**:
    *   **功能描述**: 根据需求生成各类财务报表。
    *   **报表类型**: 收入报表、支出报表、厨师业绩报表（收入、订单数）、平台利润报表。支持按日、周、月以及按厨师、按服务类型等维度进行筛选和导出。
    *   **负责人**:
        *   `[财务对账]`: 主要使用方。
        *   `[运营管理]`: 查看厨师业绩等运营相关报表。

---

#### **五、 系统管理模块 (System Administration)**

负责整个平台的底层配置和安全。

1.  **管理员账户管理**:
    *   **功能描述**: 创建、编辑、禁用后台管理员账号。
    *   **负责人**: `[系统管理]`。

2.  **角色与权限管理**:
    *   **功能描述**: 为`调度员`、`运营管理`、`财务对账`等角色分配或调整其可访问的菜单和可操作的功能权限。
    *   **负责人**: `[系统管理]`。

3.  **平台核心参数配置**:
    *   **功能描述**: 配置全局性的业务参数。
    *   **可配置项**: 平台抽成比例、各等级厨师的定义、退款政策条款、第三方服务（短信、支付、eID）的API密钥等。
    *   **负责人**: `[系统管理]`。

4.  **系统日志**:
    *   **功能描述**: 查看所有关键操作的日志（如谁在什么时间修改了订单）和系统运行的错误日志。
    *   **负责人**: `[系统管理]`。 