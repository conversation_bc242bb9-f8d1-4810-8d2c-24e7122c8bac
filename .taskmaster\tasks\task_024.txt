# Task ID: 24
# Title: 订单详情页
# Status: pending
# Dependencies: 23
# Priority: high
# Description: 开发订单详情页面，该页面内容和可操作项必须随订单状态实时变化。展示订单状态、服务信息、厨师信息（已分配时）、账单明细、订单信息和动态操作按钮区。
# Details:
页面根据订单状态（`待付款`、`待接单`、`待服务`、`服务中`、`待评价`、`已完成`等）条件渲染不同模块和按钮。厨师信息卡片在厨师已分配时显示，并提供“联系厨师”按钮。

# Test Strategy:
测试不同订单状态下，详情页内容和操作按钮是否正确显示；验证“联系厨师”按钮功能；检查账单明细是否准确。

# Subtasks:
## 1. Setup Base Page Structure & Order Data Fetching [pending]
### Dependencies: None
### Description: Create the foundational UI structure for the order detail page and implement the API call to fetch specific order data based on an order ID.
### Details:
This includes setting up the main layout, loading indicators, and error handling for data retrieval.

## 2. Define Order Status States & Data Mapping [pending]
### Dependencies: None
### Description: Identify all possible order statuses (e.g., pending, confirmed, in-service, completed, cancelled) and define how these statuses will be represented in the fetched order data model.
### Details:
Establish a clear mapping between backend status codes and frontend display logic.

## 3. Implement Core Order Detail Display [pending]
### Dependencies: 24.1
### Description: Develop UI components to display static and semi-static order details such as service name, service description, chef information (name, rating), and billing summary (total cost, payment status).
### Details:
Focus on presenting the essential information clearly, assuming data is available from Subtask 1.

## 4. Develop Dynamic Content Rendering Logic [pending]
### Dependencies: 24.2, 24.3
### Description: Implement conditional rendering logic to display different sections or information blocks on the page based on the order's current status (e.g., show 'Estimated Arrival' for 'In-Service', hide for 'Completed').
### Details:
This involves using the status defined in Subtask 2 to control the visibility and content of various UI elements built in Subtask 3.

## 5. Implement Conditional Action Buttons [pending]
### Dependencies: 24.2, 24.4
### Description: Develop the logic and UI for displaying context-sensitive action buttons (e.g., 'Contact Chef', 'Confirm Completion', 'Cancel Order') that appear or are enabled/disabled based on the order's status.
### Details:
Ensure button actions are correctly wired to backend APIs and their visibility aligns with the order lifecycle.

## 6. End-to-End Integration & Comprehensive Testing [pending]
### Dependencies: 24.1, 24.3, 24.4, 24.5
### Description: Integrate all developed components, ensure seamless data flow, and conduct comprehensive testing across all defined order statuses to validate dynamic content, button visibility, and functionality.
### Details:
Perform unit, integration, and user acceptance testing for various order states to ensure robustness and correctness.

