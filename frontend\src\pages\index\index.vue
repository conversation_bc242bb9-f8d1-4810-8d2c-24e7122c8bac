<template>
  <view>
    <u-navbar
      title="首页"
      :autoBack="false"
      bgColor="#ffffff"
      titleStyle="color: #000000; font-size: 32rpx; font-weight: 500;"
      :fixed="true"
      :safeAreaInsetTop="true"
    ></u-navbar>
    <view class="content">
      <image class="logo" src="/static/logo.png"></image>
      <view class="text-area">
        <text class="title">{{ title }}</text>
      </view>
      
      <u-modal v-model="showPrivacyModal" :show-title="false" :show-cancel-button="true" confirm-text="同意" cancel-text="拒绝" @confirm="onAgreePrivacy" @cancel="onDisagreePrivacy">
        <view class="privacy-content">
          <text class="privacy-title">用户隐私保护提示</text>
          <text class="privacy-text">感谢您使用ChefAtDoor！我们非常重视您的个人信息和隐私保护。为了更好地保障您的个人权益，在使用我们的产品前，请您认真阅读《用户隐私保护指引》的全部内容，同意并接受全部条款后开始使用我们的产品和服务。</text>
          <text class="privacy-text">我们将严格按照法律法规和隐私政策的要求使用和保护您的个人信息，包括获取您的手机号码用于账号登录和服务通知。</text>
          <text class="privacy-text">您可以通过阅读完整版《用户隐私保护指引》了解详细信息。</text>
        </view>
      </u-modal>

      <button class="login-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">微信一键登录</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: 'ChefAtDoor',
      showPrivacyModal: false,
      privacyResolve: null
    }
  },
  onLoad() {
    uni.onNeedPrivacyAuthorization(resolve => {
      console.log('需要隐私授权')
      this.showPrivacyModal = true
      this.privacyResolve = resolve
    })
  },
  onUnmounted() {
    // 清理监听器
    uni.offNeedPrivacyAuthorization()
  },
  methods: {
    // 点击登录按钮前先请求隐私授权
    getPhoneNumber(e) {
      // 先调用隐私授权API，它会触发onNeedPrivacyAuthorization事件
      wx.requirePrivacyAuthorize({
        success: () => {
          // 用户已同意隐私协议，继续处理手机号获取
          console.log('隐私授权成功，处理手机号', e)
          
          // 检测是否在开发者工具中运行
          const isDevTool = uni.getSystemInfoSync().platform === 'devtools';
          
          if (isDevTool) {
            // 在开发者工具中模拟手机号获取成功
            console.log('开发环境：模拟手机号获取成功');
            uni.showToast({
              title: '模拟登录成功',
              icon: 'success'
            });
            
            // 模拟获取到的手机号数据
            const mockPhoneData = {
              phoneNumber: '13800138000',
              purePhoneNumber: '13800138000',
              countryCode: '86'
            };
            
            // 这里可以调用后端API进行登录，使用模拟数据
            console.log('模拟手机号数据:', mockPhoneData);
            
            // TODO: 调用登录API
            // this.loginWithPhone(mockPhoneData);
            
            return; // 在开发环境中提前返回
          }
          
          // 真机环境处理逻辑
          if (e.detail.errMsg === 'getPhoneNumber:ok') {
            // 成功获取手机号
            console.log('获取手机号成功', e.detail)
            uni.showToast({
              title: '登录成功',
              icon: 'success'
            })
            // 这里可以调用后端API进行登录
            // this.loginWithPhone(e.detail);
          } else {
            console.log('用户拒绝授权手机号', e.detail.errMsg)
            uni.showToast({
              title: '获取手机号失败',
              icon: 'none'
            })
          }
        },
        fail: (err) => {
          // 用户拒绝隐私协议
          console.log('用户拒绝隐私授权', err)
          uni.showToast({
            title: '需要同意隐私协议才能继续',
            icon: 'none'
          })
        }
      })
    },
    
    // 可以添加一个登录方法，接收手机号数据
    loginWithPhone(phoneData) {
      // TODO: 实现登录逻辑
      console.log('使用手机号登录:', phoneData);
      // 这里可以调用后端API
    },
    
    onAgreePrivacy() {
      console.log('用户同意隐私协议')
      if (this.privacyResolve) {
        // 告知微信用户已同意隐私政策
        this.privacyResolve({ event: 'agree', buttonId: 'agree-btn' })
        this.privacyResolve = null
      }
      this.showPrivacyModal = false
    },
    
    onDisagreePrivacy() {
      console.log('用户拒绝隐私协议')
      if (this.privacyResolve) {
        // 告知微信用户已拒绝隐私政策
        this.privacyResolve({ event: 'disagree' })
        this.privacyResolve = null
      }
      this.showPrivacyModal = false
      uni.showToast({
        title: '需要同意隐私协议才能继续',
        icon: 'none'
      })
    }
  }
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  margin-top: 90rpx; /* 为固定导航栏留出空间 */
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 100rpx;
  margin-bottom: 50rpx;
}

.text-area {
  display: flex;
  justify-content: center;
  margin-bottom: 50rpx;
}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}

.login-btn {
  margin-top: 80rpx;
  background-color: #07c160;
  color: #ffffff;
  width: 80%;
  border-radius: 10rpx;
  padding: 20rpx 0;
}

.privacy-content {
  padding: 30rpx;
}

.privacy-title {
  display: block;
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.privacy-text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  text-align: justify;
  line-height: 1.6;
}
</style>
