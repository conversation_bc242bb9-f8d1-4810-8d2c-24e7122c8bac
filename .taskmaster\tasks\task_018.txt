# Task ID: 18
# Title: “单点厨师”模式厨师列表
# Status: pending
# Dependencies: 8
# Priority: high
# Description: 开发“单点厨师”服务模式的厨师列表页面。页面顶部提供按菜系筛选Tab、综合排序功能（综合推荐、销量最高、评价最好），以及多维度组合筛选（厨师等级、服务费范围等）。厨师卡片需清晰展示核心信息。
# Details:
筛选Tab使用`u-tabs`，排序功能使用`u-dropdown`或自定义下拉菜单。组合筛选可使用侧边弹窗或新页面。厨师卡片使用自定义组件，包含头像、姓名、评分、服务费、标签等。

# Test Strategy:
测试筛选和排序功能是否正常工作，结果是否准确；验证厨师卡片信息展示是否完整且美观；检查列表加载性能和分页功能。

# Subtasks:
## 1. Backend API Integration for Chef Data Retrieval [pending]
### Dependencies: None
### Description: Develop and integrate the necessary backend API endpoints to fetch chef data, supporting initial list retrieval, filtering, and sorting parameters.
### Details:
This involves defining data models for chefs, implementing database queries, and exposing RESTful endpoints for chef list access.

## 2. Implement Cuisine Filtering Tabs UI/Logic [pending]
### Dependencies: 18.1
### Description: Design and implement the user interface for cuisine filtering tabs, allowing users to select specific cuisines to filter the chef list. Integrate with the backend API to apply the selected cuisine filter.
### Details:
Create interactive tabs for different cuisines (e.g., Italian, French, Asian). Ensure selection updates the displayed chef list via API calls.

## 3. Develop Sorting Options UI/Logic [pending]
### Dependencies: 18.1
### Description: Create dropdowns or similar UI elements for sorting the chef list based on criteria like 'Rating (High to Low)', 'Price (Low to High)', 'Experience', etc. Integrate with the backend API to apply the selected sort order.
### Details:
Implement client-side logic to send sorting parameters to the backend API and update the chef list display accordingly.

## 4. Build Multi-Dimensional Combination Filters [pending]
### Dependencies: 18.2, 18.3
### Description: Implement advanced filtering options that allow users to combine multiple criteria (e.g., dietary restrictions, availability, specializations) beyond just cuisine. Ensure these filters can be applied in conjunction with cuisine tabs and sorting.
### Details:
Design a filter panel or modal with checkboxes/dropdowns for various attributes. Ensure complex queries are correctly formed and sent to the backend API.

## 5. Design and Implement Chef Card Display [pending]
### Dependencies: 18.1
### Description: Develop the visual component for individual chef cards, displaying key information such as chef name, cuisine, rating, price range, and a profile picture. Ensure responsiveness and clear presentation.
### Details:
Create a reusable UI component for a chef card. Populate it with data fetched from the API, considering placeholders for missing information.

## 6. Implement Pagination and Infinite Loading [pending]
### Dependencies: 18.1, 18.5
### Description: Develop the mechanism for loading more chefs, either through traditional pagination (next/previous buttons) or infinite scrolling, to efficiently handle large datasets.
### Details:
Implement client-side logic to request subsequent pages/batches of data from the API. Include loading indicators and handle end-of-list scenarios.

