# Task ID: 26
# Title: 个人中心主页
# Status: pending
# Dependencies: 3, 5
# Priority: high
# Description: 开发个人中心主页，顶部展示用户的微信头像和昵称，并根据用户身份显示VIP标识。页面下方以网格或列表形式，清晰展示各项核心功能入口，如“我的订单”、“地址管理”、“优惠券”等。
# Details:
个人信息区使用`u-avatar`和`u-text`展示。功能入口使用`u-grid`或`u-cell-group`布局，每个入口点击后跳转到对应页面。VIP标识根据用户数据条件渲染。

# Test Strategy:
测试个人信息展示是否正确；验证所有功能入口是否可点击，并能正确导航到对应页面；检查VIP标识的显示逻辑。

# Subtasks:
## 1. Design Personal Center Base Layout [pending]
### Dependencies: None
### Description: Outline the main structural components of the personal center page, including header, user info section, navigation area, and a main content display area.
### Details:
Wireframe the overall page flow and sectioning, defining primary containers and their relationships.

## 2. Integrate User Avatar & VIP Status Display [pending]
### Dependencies: 26.1
### Description: Develop the UI component for displaying the user's avatar, nickname, and conditionally showing VIP status based on user data within the designated user info section.
### Details:
Design avatar placeholder, text styles for nickname, and a distinct badge/indicator for VIP status (e.g., 'VIP' text, special icon, or color).

## 3. Create Core Sub-Module Navigation Links [pending]
### Dependencies: 26.1
### Description: Implement clickable navigation links for core sub-modules such as 'My Orders,' 'Address Management,' and 'My Coupons,' ensuring they are clearly visible and accessible within the navigation area.
### Details:
Design navigation menu/list, define link styles, and consider icon integration for each module to enhance usability.

## 4. Apply Basic Styling & Responsiveness [pending]
### Dependencies: 26.1, 26.2, 26.3
### Description: Apply basic CSS styling to the personal center layout and ensure it is responsive across different screen sizes (desktop, tablet, mobile) for optimal user experience.
### Details:
Define a consistent color palette, typography, spacing, and implement media queries for responsive adjustments to layout elements.

