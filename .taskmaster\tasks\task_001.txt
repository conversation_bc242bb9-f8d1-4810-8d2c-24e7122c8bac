# Task ID: 1
# Title: 项目初始化与基础框架配置
# Status: done
# Dependencies: None
# Priority: high
# Description: 初始化uni-app项目，配置Vue 3的<script setup>语法，集成uView Plus组件库和UnoCSS原子化CSS框架。同时，创建Pinia的状态管理目录结构（如`stores`目录），为后续状态管理预留位置。
# Details:
使用HBuilderX或Vite CLI创建uni-app项目。在`main.js`中引入并配置uView Plus和Pinia。配置`vite.config.js`以支持UnoCSS。创建`src/stores`目录。

# Test Strategy:
验证项目是否能成功编译和运行；检查uView Plus和UnoCSS是否正确集成，基础组件和样式是否生效；确认`stores`目录结构已创建。
