<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
onLaunch(() => {
  console.log("App Launch");

  // 监听微信小程序需要用户授权隐私的事件
  if (uni.onNeedPrivacyAuthorization) {
    uni.onNeedPrivacyAuthorization(resolve => {
      console.log('App.vue: 触发 onNeedPrivacyAuthorization');
      // 通过全局事件总线通知页面显示隐私弹窗
      uni.$emit('needPrivacyAuthorization', { resolve });
    });
  }
});
onShow(() => {
  console.log("App Show");
});
onHide(() => {
  console.log("App Hide");
});
</script>
<style lang="scss">
@import "uview-plus/theme.scss";
@import "uview-plus/index.scss";
</style>
