import { defineConfig } from 'unocss'
import { presetUni } from '@uni-helper/unocss-preset-uni'

export default defineConfig({
  presets: [
    presetUni(),
  ],
  shortcuts: {
    'border-base': 'border-gray-200 dark:border-dark-200',
    'bg-base': 'bg-white dark:bg-dark-100',
    'bg-surface': 'bg-gray-100 dark:bg-dark-200',
    'flex-center': 'flex justify-center items-center',
    'page-container': 'p-4',
    'rounded-base': 'rounded-lg',
    'btn': 'px-4 py-2 rounded-lg text-white',
    'btn-primary': 'bg-primary hover:bg-primary-dark',
  },
  theme: {
    colors: {
      primary: '#FF6347',
      'primary-dark': '#E5533D',
    },
  },
}) 