{"tasks": [{"id": 1, "title": "项目初始化与基础框架配置", "description": "初始化uni-app项目，配置Vue 3的<script setup>语法，集成uView Plus组件库和UnoCSS原子化CSS框架。同时，创建Pinia的状态管理目录结构（如`stores`目录），为后续状态管理预留位置。", "details": "使用HBuilderX或Vite CLI创建uni-app项目。在`main.js`中引入并配置uView Plus和Pinia。配置`vite.config.js`以支持UnoCSS。创建`src/stores`目录。", "testStrategy": "验证项目是否能成功编译和运行；检查uView Plus和UnoCSS是否正确集成，基础组件和样式是否生效；确认`stores`目录结构已创建。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 2, "title": "全局样式与主题定义", "description": "基于UnoCSS的原子化CSS能力和uView Plus的主题配置，定义项目全局的颜色、字体、间距等样式变量。确保所有界面布局以iPhone 16 Pro的屏幕尺寸为基准，并具备良好的响应式表现。", "details": "在`uno.config.ts`中配置UnoCSS的预设和自定义规则，定义常用颜色变量。利用uView Plus的主题定制功能，统一组件样式。使用`rem`或`vw/vh`等相对单位进行布局，确保在不同设备上的适配性。", "testStrategy": "在不同尺寸的模拟器（如iPhone 16 Pro, Android主流机型）上测试页面布局和样式是否一致且美观；验证全局颜色和字体是否按设计稿呈现。", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 3, "title": "底部Tab Bar与顶部导航栏开发", "description": "在`pages.json`中配置uni-app的原生底部Tab Bar，包含“首页”、“订单”、“我的”等核心入口。同时，为所有页面设计并实现统一的`u-navbar`组件作为顶部导航栏，确保标题清晰。", "details": "在`pages.json`的`tabBar`字段中配置列表、颜色、图标等。在`App.vue`或每个页面顶部使用`u-navbar`组件，设置标题和背景色，确保与整体UI风格一致。", "testStrategy": "验证Tab Bar的切换功能是否正常，图标和文字是否正确显示；检查所有页面的顶部导航栏是否统一，标题是否居中且清晰。", "priority": "high", "dependencies": [1, 2], "status": "pending", "subtasks": []}, {"id": 4, "title": "通用网络请求模块封装", "description": "封装基于uni.request的HTTP请求模块，统一处理请求头、错误拦截、Loading状态等。定义后端API接口规范，并预留Mock数据集成点，以便前端独立开发和测试。", "details": "创建`utils/request.js`或`api/index.js`文件，封装`uni.request`。实现请求拦截器和响应拦截器，处理token、错误码等。定义常用的API接口常量，例如`API_BASE_URL`。", "testStrategy": "编写单元测试，验证请求拦截器和响应拦截器是否按预期工作；通过Postman或Mock数据验证API请求和响应的正确性。", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 5, "title": "微信一键登录与隐私协议功能", "description": "实现微信一键登录功能，通过调用微信官方接口获取用户手机号进行注册或登录。开发隐私协议和服务协议的弹窗和详情页，确保用户在授权前必须同意相关协议。", "details": "使用`uni.login`获取code，然后调用后端接口换取用户手机号和OpenID。前端根据后端返回的登录状态（新用户/老用户）进行相应处理。隐私协议弹窗使用`u-modal`或自定义组件，协议内容通过`u-parse`或`web-view`展示。", "testStrategy": "测试新用户和老用户的登录流程是否顺畅；验证用户拒绝授权时的友好提示；检查隐私协议和服务协议链接是否可点击，内容是否完整展示；确保未勾选协议时登录按钮不可用。", "priority": "high", "dependencies": [4], "status": "done", "subtasks": [{"id": 1, "title": "WeChat Developer Account Setup & API Key Acquisition", "description": "Register a WeChat developer account, create an application, and obtain the App ID and App Secret required for API integration and configuration.", "dependencies": [], "details": "This involves navigating the WeChat Open Platform, submitting necessary documentation, and configuring the application for web or mobile login.", "status": "done"}, {"id": 2, "title": "Frontend WeChat SDK Integration", "description": "Integrate the appropriate WeChat SDK (e.g., JS-SDK for web, or relevant mobile SDK for iOS/Android) into the client-side application to enable WeChat login functionality.", "dependencies": [1], "details": "Configure the SDK with the obtained App ID and ensure proper initialization and readiness for API calls.", "status": "done"}, {"id": 3, "title": "Frontend WeChat Login Button & Authorization Flow Implementation", "description": "Develop the UI for the 'Login with WeChat' button and implement the client-side logic to initiate the WeChat authorization flow, capturing the authorization code upon successful user authentication.", "dependencies": [2], "details": "This includes handling redirects, pop-ups, and securely receiving the authorization code from WeChat's callback.", "status": "done"}, {"id": 4, "title": "Backend API for WeChat Authentication & User Management", "description": "Create backend endpoints to receive the WeChat authorization code, exchange it for access tokens and user information, and handle user registration (if new) or login (if existing) in the application's database.", "dependencies": [3], "details": "Implement secure token exchange, user data fetching, and mapping WeChat user IDs to internal user accounts, including session management.", "status": "done"}, {"id": 5, "title": "Privacy Agreement Modal/Page Development", "description": "Design and implement the user interface for the privacy protocol agreement, presented as a modal or dedicated page, detailing data usage, user rights, and consent options.", "dependencies": [], "details": "Ensure the UI is clear, legally compliant, and provides explicit options for users to accept or decline the terms.", "status": "done"}, {"id": 6, "title": "User Consent Handling & Enforcement Logic", "description": "Implement the backend and frontend logic to record user consent to the privacy agreement, store it securely, and enforce application behavior based on the user's consent status (e.g., requiring consent before proceeding with login or data access).", "dependencies": [4, 5], "details": "This involves updating user profiles with consent status, auditing consent changes, and conditionally enabling features based on consent.", "status": "done"}]}, {"id": 6, "title": "地理位置服务与城市切换", "description": "在应用启动时，弹出隐私协议并请求获取地理位置权限。在首页顶部展示用户当前定位信息，并提供手动切换城市或地址的功能。", "details": "使用`uni.getLocation`获取用户位置，通过逆地理编码API（如腾讯地图SDK）解析地址。城市切换功能可设计为弹窗或新页面，提供城市列表供用户选择，并支持搜索。", "testStrategy": "测试地理位置权限请求弹窗是否正常弹出；验证首页定位信息是否准确；测试手动切换城市或地址后，首页显示是否更新。", "priority": "high", "dependencies": [3, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "首页布局与全局搜索功能", "description": "实现首页整体模块化布局，包括顶部的搜索栏、轮播广告位、核心服务入口矩阵、推荐内容模块。开发全局搜索框，支持按厨师名称、菜系、套餐名称等关键词进行搜索。", "details": "使用uView Plus的`u-search`组件作为搜索框。首页布局采用`u-grid`、`u-swiper`、`u-list`等组件组合。搜索功能需调用后端API进行模糊查询，并展示搜索结果页面。", "testStrategy": "验证首页各模块布局是否合理，视觉效果是否美观；测试搜索框输入关键词后，搜索结果是否准确显示，无异常报错。", "priority": "high", "dependencies": [3, 6], "status": "pending", "subtasks": [{"id": 1, "title": "Homepage Core Layout & Module Design", "description": "Design and implement the foundational layout for the homepage, including placeholders/initial designs for non-search modules like carousels, product grids, and recommendation sections. Focus on responsive design and overall page structure.", "dependencies": [], "details": "This task establishes the visual framework for the homepage, ensuring all major content areas are defined before integrating specific functionalities.", "status": "pending"}, {"id": 2, "title": "Global Search Bar UI/UX Development", "description": "Design and implement the interactive user interface for the global search bar, including input field, search icon, clear button, and basic auto-suggest/autocomplete dropdown styling. Focus on usability and visual consistency.", "dependencies": [1], "details": "This covers the frontend development of the search input component that will be placed within the homepage layout.", "status": "pending"}, {"id": 3, "title": "Search API Backend Development", "description": "Develop the backend API endpoints for global search, including data indexing, query processing, and returning relevant search results. Define the API contract for frontend consumption.", "dependencies": [], "details": "This task focuses on the server-side logic and data retrieval necessary for the search functionality, independent of the frontend UI.", "status": "pending"}, {"id": 4, "title": "Search Results Display UI/UX Development", "description": "Design and implement the user interface for displaying search results, including result cards, pagination, filtering/sorting options, and handling no-results states. Ensure a clear and intuitive presentation of search outcomes.", "dependencies": [2], "details": "This task builds the visual components for presenting search results, which will be triggered by the search bar.", "status": "pending"}, {"id": 5, "title": "Frontend-Backend Search Integration", "description": "Integrate the frontend search components (search bar, results display) with the developed backend search API. Implement API calls, data parsing, error handling, and state management for the search flow.", "dependencies": [2, 3, 4], "details": "This crucial task connects the frontend user experience with the backend data, making the search functionality fully operational.", "status": "pending"}, {"id": 6, "title": "Homepage & Search Performance Optimization & Testing", "description": "Optimize the loading performance of the entire homepage and the responsiveness of the global search functionality. This includes asset optimization, lazy loading, caching strategies, and thorough functional and performance testing.", "dependencies": [1, 5], "details": "This final task ensures the homepage and search are fast, reliable, and provide a smooth user experience across various devices and network conditions.", "status": "pending"}]}, {"id": 8, "title": "首页轮播广告与核心服务入口", "description": "在首页实现一个可由后台配置的轮播图组件，用于展示平台活动或推广。同时，以网格菜单形式清晰展示平台提供的所有服务模式，如“一口价加工”、“精选套餐”等。", "details": "轮播图使用`u-swiper`组件，图片数据从后端获取。核心服务入口使用`u-grid`或自定义网格布局，每个入口点击后跳转到对应的服务模式页面。", "testStrategy": "测试轮播图是否自动播放，点击是否能正确跳转；验证核心服务入口图标和文字是否清晰，点击是否能正确导航到对应页面。", "priority": "high", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 9, "title": "首页内容推荐模块开发", "description": "开发首页的明星厨师推荐模块（横向滚动列表）和热门套餐推荐模块（图文卡片）。确保使用来自Unsplash、Pexels等源的高质量、真实的图片资源，避免占位符。", "details": "明星厨师推荐使用`u-scroll-list`或`scroll-view`实现横向滚动，每个厨师卡片包含照片、姓名、评分、厨龄、基础服务费和个人格言。热门套餐推荐使用`u-card`或自定义卡片布局，包含图片、名称、价格、适用人数、菜品数量。", "testStrategy": "验证推荐模块的数据加载和展示是否正常；检查图片加载速度和质量；测试横向滚动和卡片点击跳转是否流畅。", "priority": "high", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 10, "title": "地址管理模块开发", "description": "开发用户地址管理模块，包括地址列表展示、新增地址、编辑地址和删除地址功能。支持用户设置默认地址，并在预约流程中方便地选择已保存的地址。", "details": "地址列表页使用`u-list`展示，每个地址项包含收货人、手机号、详细地址。新增/编辑地址页包含表单输入（姓名、手机、省市区选择、详细地址），并提供“设为默认地址”开关。地址数据通过API与后端交互。", "testStrategy": "测试地址的增删改查功能是否正常；验证默认地址设置和选择是否生效；检查手机号等字段的输入校验。", "priority": "high", "dependencies": [4, 5], "status": "pending", "subtasks": []}, {"id": 11, "title": "增值服务选择组件开发", "description": "开发一个可复用的增值服务选择组件，该组件应在所有服务模式的预约流程中集成。支持用户通过勾选框选择所需服务，对于需要指定数量的服务（如餐具），提供步进器进行数量调整，并实时联动订单总价。", "details": "组件内部维护增值服务列表，每个服务项包含名称、价格、计费方式。使用`u-checkbox-group`或`u-switch`进行选择，`u-number-box`作为步进器。通过`emit`事件将选择结果和计算后的费用传递给父组件。", "testStrategy": "测试增值服务的选择和数量调整功能是否正常；验证订单总价是否实时更新；检查不同服务模式下增值服务的可用性限制。", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 12, "title": "优惠券选择与计算模块", "description": "开发优惠券选择页面，展示用户可用的优惠券列表，并清晰标注折扣额度、使用门槛、适用范围和有效期。同时，展示不可用优惠券及其原因。支持用户选择优惠券或不使用优惠，并实时计算订单优惠金额。", "details": "优惠券列表使用`u-list`或自定义卡片展示。可用和不可用优惠券通过Tab或折叠面板区分。使用`u-radio-group`进行单选。选择后，通过API计算优惠金额并更新订单总价。", "testStrategy": "测试优惠券列表的加载和展示是否正确；验证优惠券选择后，订单总价是否正确计算；检查不可用优惠券的提示信息是否准确。", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 13, "title": "通用订单确认与支付流程", "description": "开发通用的订单确认页面，汇总展示所有订单信息（服务模式、时间、地址、费用明细）。集成微信支付功能，实现用户通过微信支付完成订单。开发支付成功和失败的提示页面。", "details": "订单确认页使用`u-cell-group`和自定义布局展示各项信息。支付按钮点击后调用`uni.requestPayment`拉起微信支付。支付结果通过后端回调确认，前端跳转到对应的结果页。", "testStrategy": "测试订单信息汇总是否准确；验证微信支付流程是否顺畅，支付成功和失败的页面跳转是否正确；检查支付状态与后端同步是否一致。", "priority": "high", "dependencies": [4, 10, 11, 12], "status": "pending", "subtasks": [{"id": 1, "title": "Develop Order Confirmation Page Frontend", "description": "Design and implement the user interface for the order confirmation page, displaying order details, total amount, and a 'Proceed to Pay' button.", "dependencies": [], "details": "Includes UI/UX design, data binding for order details, and basic page navigation.", "status": "pending"}, {"id": 2, "title": "Implement Backend Payment Initiation & Callback Endpoints", "description": "Develop backend APIs to create a payment order, generate WeChat Pay parameters (e.g., prepay_id), and set up a secure callback endpoint for WeChat to notify payment status.", "dependencies": [], "details": "Involves secure API key management, signature generation, and initial database schema for payment records.", "status": "pending"}, {"id": 3, "title": "Integrate WeChat Pay (`uni.requestPayment`) on Frontend", "description": "Implement the frontend logic to call the backend payment initiation API (Subtask 2) and then invoke `uni.requestPayment` with the received parameters.", "dependencies": [1, 2], "details": "Handle network requests, parameter parsing, and error handling for the payment initiation call.", "status": "pending"}, {"id": 4, "title": "Develop Frontend Payment Result Handling Logic", "description": "Implement the logic to process the result of `uni.requestPayment` (success/fail/cancel) and redirect the user to the appropriate dedicated success or failure page.", "dependencies": [3], "details": "Includes parsing the `uni.requestPayment` response, conditional redirection, and basic error logging.", "status": "pending"}, {"id": 5, "title": "Develop Dedicated Payment Success Page", "description": "Create a dedicated frontend page to display a successful payment confirmation, including order number and next steps (e.g., view order history).", "dependencies": [4], "details": "Focus on clear success messaging, user-friendly design, and relevant post-payment actions.", "status": "pending"}, {"id": 6, "title": "Develop Dedicated Payment Failure Page", "description": "Create a dedicated frontend page to inform the user about a payment failure, providing reasons (if available) and options to retry or contact support.", "dependencies": [4], "details": "Focus on clear failure messaging, troubleshooting tips, and options for re-attempting payment or seeking help.", "status": "pending"}]}, {"id": 14, "title": "找人代付与订阅消息授权", "description": "实现订单的“找人代付”功能，生成代付分享卡片供用户发送给微信好友。在支付成功后的页面上，引导用户授权订阅消息，以便接收订单状态更新通知。", "details": "找人代付通过生成特定订单ID的分享链接实现，好友点击链接进入代付页面。订阅消息授权使用`uni.requestSubscribeMessage`，在支付成功后弹出，引导用户允许接收通知。", "testStrategy": "测试找人代付链接的生成和分享是否正常，代付人能否成功支付；验证订阅消息授权弹窗是否在支付成功后弹出，用户选择允许或取消后是否不影响主流程。", "priority": "medium", "dependencies": [13], "status": "pending", "subtasks": []}, {"id": 15, "title": "退款政策展示页面", "description": "在订单确认页的费用总计下方或支付按钮附近，提供一个清晰的“退款政策”链接。点击链接后，弹出一个浮层或页面，以清晰易懂的语言展示阶梯式退款规则。", "details": "退款政策内容可从后端获取或前端硬编码，使用`u-popup`或新页面展示。内容应以列表或时间轴形式清晰列出不同时间节点对应的退款比例。", "testStrategy": "测试退款政策链接是否可点击，弹窗或页面是否正常显示；验证退款规则内容是否清晰易懂，无歧义。", "priority": "low", "dependencies": [13], "status": "pending", "subtasks": []}, {"id": 16, "title": "“一口价加工”模式页面", "description": "开发“一口价加工”服务模式的页面，包括服务介绍、不同规格加工套餐的选择（如四菜一汤、六菜一汤），以及预约信息填写页面。预约页需支持菜品数量调整和实时费用更新。", "details": "套餐选择使用卡片列表展示，点击进入预约页。预约页包含地址选择（复用地址管理模块）、用餐时间选择器、菜品数量步进器（`u-number-box`），并根据选择实时计算总价。", "testStrategy": "测试套餐选择和预约流程是否顺畅；验证菜品数量调整后，总价是否正确更新；检查增值服务选择是否正常集成。", "priority": "high", "dependencies": [8, 10, 11, 13], "status": "pending", "subtasks": [{"id": 1, "title": "Page Structure & UI Framework Setup", "description": "Set up the foundational HTML structure, CSS styling, and integrate the necessary UI framework components for the 'fixed-price processing' mode page.", "dependencies": [], "details": "Establish the basic layout, header, footer, and content areas. Ensure responsiveness and adherence to design guidelines.", "status": "pending"}, {"id": 2, "title": "Service Introduction & Package Selection UI Development", "description": "Develop the user interface for displaying the service introduction, fixed-price package options, and enabling user selection of packages.", "dependencies": [1], "details": "Implement package cards/lists, selection mechanisms (e.g., radio buttons, checkboxes), and display of package details (price, inclusions).", "status": "pending"}, {"id": 3, "title": "Appointment Information Form Implementation", "description": "Create and integrate the form fields required for collecting appointment-specific information, such as date, time, special instructions, and contact details.", "dependencies": [1], "details": "Develop input fields, date/time pickers, validation rules, and error handling for the appointment form.", "status": "pending"}, {"id": 4, "title": "Dynamic Pricing Calculation Logic Development", "description": "Implement the backend and frontend logic to dynamically calculate the total price based on the selected fixed-price package, quantities, and any additional specifications.", "dependencies": [2, 3], "details": "Develop API endpoints for pricing, integrate with frontend to update price in real-time, and handle edge cases for quantity/spec variations.", "status": "pending"}, {"id": 5, "title": "Common Module Integration (Address & Value-Added Services)", "description": "Integrate the 'fixed-price processing' page with existing common modules for address management (selection/input) and the display/selection of value-added services.", "dependencies": [3, 4], "details": "Connect to address lookup/management APIs, display available value-added services, and ensure their selection impacts the dynamic pricing.", "status": "pending"}, {"id": 6, "title": "Universal Payment Flow Integration & End-to-End Testing", "description": "Integrate the final order details and calculated price with the universal payment gateway and conduct comprehensive end-to-end testing of the entire fixed-price processing flow.", "dependencies": [4, 5], "details": "Pass order data to the payment module, handle payment callbacks, and perform user acceptance testing (UAT) for the complete user journey.", "status": "pending"}]}, {"id": 17, "title": "“精选套餐”模式列表与详情", "description": "开发“精选套餐”服务模式的套餐列表页和套餐详情页。套餐列表以精美图文卡片形式展示，详情页包含套餐主图、名称、价格、菜品清单和服务说明，并接入通用预约流程。", "details": "套餐列表使用`u-grid`或`u-list`展示卡片。套餐详情页使用`u-swiper`展示主图，菜品清单可使用`u-collapse`或`u-list`分组展示。点击“立即预订”跳转到通用预约页。", "testStrategy": "测试套餐列表和详情页的数据加载和展示是否正常；验证点击“立即预订”后，是否能正确跳转到预约页并带入套餐信息。", "priority": "high", "dependencies": [8, 10, 11, 13], "status": "pending", "subtasks": [{"id": 1, "title": "Define Package Data Model & API Endpoints", "description": "Design the backend data structure for featured packages (e.g., name, description, price, images, services included) and define the necessary API endpoints for fetching package lists and individual package details.", "dependencies": [], "details": "This involves collaboration with backend team to finalize schema and RESTful endpoints.", "status": "pending"}, {"id": 2, "title": "Develop Featured Package List Page UI", "description": "Create the front-end UI for displaying a list of featured packages, including package name, price, and a placeholder for a thumbnail image. Implement initial data fetching from the defined API endpoint.", "dependencies": [1], "details": "Focus on responsive layout and basic information display for each package card.", "status": "pending"}, {"id": 3, "title": "Implement Package List Image Handling", "description": "Develop the logic for efficiently loading, displaying, and caching thumbnail images for packages on the list page, ensuring responsiveness and performance.", "dependencies": [2], "details": "Consider image optimization techniques (e.g., lazy loading, responsive images) and error handling for missing images.", "status": "pending"}, {"id": 4, "title": "Develop Featured Package Detail Page UI", "description": "Design and implement the front-end UI for the individual package detail page, displaying comprehensive information such as full description, included services, menu details, and pricing options. Implement data fetching for a single package.", "dependencies": [1, 2], "details": "Ensure all relevant package attributes are displayed clearly and attractively. Include sections for 'What's Included', 'Menu', 'Pricing Options'.", "status": "pending"}, {"id": 5, "title": "Implement Package Detail Image Gallery", "description": "Develop a robust image gallery or carousel component for the package detail page to showcase multiple high-resolution images associated with the package.", "dependencies": [4], "details": "Include features like full-screen view, navigation controls, and image captions.", "status": "pending"}, {"id": 6, "title": "Integrate Universal Booking with Package Detail", "description": "Connect the package detail page with the existing universal booking process, allowing users to select a package, choose options (e.g., date, time, number of guests), and proceed to booking directly from the detail page.", "dependencies": [4], "details": "This involves passing selected package ID and options to the booking flow, ensuring a seamless user experience.", "status": "pending"}]}, {"id": 18, "title": "“单点厨师”模式厨师列表", "description": "开发“单点厨师”服务模式的厨师列表页面。页面顶部提供按菜系筛选Tab、综合排序功能（综合推荐、销量最高、评价最好），以及多维度组合筛选（厨师等级、服务费范围等）。厨师卡片需清晰展示核心信息。", "details": "筛选Tab使用`u-tabs`，排序功能使用`u-dropdown`或自定义下拉菜单。组合筛选可使用侧边弹窗或新页面。厨师卡片使用自定义组件，包含头像、姓名、评分、服务费、标签等。", "testStrategy": "测试筛选和排序功能是否正常工作，结果是否准确；验证厨师卡片信息展示是否完整且美观；检查列表加载性能和分页功能。", "priority": "high", "dependencies": [8], "status": "pending", "subtasks": [{"id": 1, "title": "Backend API Integration for Chef Data Retrieval", "description": "Develop and integrate the necessary backend API endpoints to fetch chef data, supporting initial list retrieval, filtering, and sorting parameters.", "dependencies": [], "details": "This involves defining data models for chefs, implementing database queries, and exposing RESTful endpoints for chef list access.", "status": "pending"}, {"id": 2, "title": "Implement Cuisine Filtering Tabs UI/Logic", "description": "Design and implement the user interface for cuisine filtering tabs, allowing users to select specific cuisines to filter the chef list. Integrate with the backend API to apply the selected cuisine filter.", "dependencies": [1], "details": "Create interactive tabs for different cuisines (e.g., Italian, French, Asian). Ensure selection updates the displayed chef list via API calls.", "status": "pending"}, {"id": 3, "title": "Develop Sorting Options UI/Logic", "description": "Create dropdowns or similar UI elements for sorting the chef list based on criteria like 'Rating (High to Low)', 'Price (Low to High)', 'Experience', etc. Integrate with the backend API to apply the selected sort order.", "dependencies": [1], "details": "Implement client-side logic to send sorting parameters to the backend API and update the chef list display accordingly.", "status": "pending"}, {"id": 4, "title": "Build Multi-Dimensional Combination Filters", "description": "Implement advanced filtering options that allow users to combine multiple criteria (e.g., dietary restrictions, availability, specializations) beyond just cuisine. Ensure these filters can be applied in conjunction with cuisine tabs and sorting.", "dependencies": [2, 3], "details": "Design a filter panel or modal with checkboxes/dropdowns for various attributes. Ensure complex queries are correctly formed and sent to the backend API.", "status": "pending"}, {"id": 5, "title": "Design and Implement Chef Card Display", "description": "Develop the visual component for individual chef cards, displaying key information such as chef name, cuisine, rating, price range, and a profile picture. Ensure responsiveness and clear presentation.", "dependencies": [1], "details": "Create a reusable UI component for a chef card. Populate it with data fetched from the API, considering placeholders for missing information.", "status": "pending"}, {"id": 6, "title": "Implement Pagination and Infinite Loading", "description": "Develop the mechanism for loading more chefs, either through traditional pagination (next/previous buttons) or infinite scrolling, to efficiently handle large datasets.", "dependencies": [1, 5], "details": "Implement client-side logic to request subsequent pages/batches of data from the API. Include loading indicators and handle end-of-list scenarios.", "status": "pending"}]}, {"id": 19, "title": "“单点厨师”模式厨师详情与预约", "description": "开发“单点厨师”模式的厨师详情页面，包含招牌套餐、个人主页、用户评价、可约时间等Tab式内容区。实现两种预约路径：预约厨师基础服务和预订厨师个人套餐，并接入通用预约流程。", "details": "厨师详情页使用`u-tabs`切换内容。招牌套餐Tab展示厨师个人套餐列表。可约时间Tab使用日历组件展示厨师档期。预约按钮点击后，根据路径跳转到通用预约页，并带入厨师或套餐信息。", "testStrategy": "测试厨师详情页各Tab内容切换是否正常；验证两种预约路径是否能正确跳转到预约页，并带入相应数据；检查厨师可约时间日历显示是否准确。", "priority": "high", "dependencies": [18, 10, 11, 13], "status": "pending", "subtasks": [{"id": 1, "title": "Chef Detail Page Core Structure", "description": "Set up the foundational layout and shell for the 'à la carte chef' detail page, including header, main content area, and basic styling.", "dependencies": [], "details": "This involves creating the main HTML/component structure and initial CSS for the chef's dedicated page.", "status": "pending"}, {"id": 2, "title": "Multi-Tab Navigation Implementation", "description": "Develop the interactive tab navigation system for the chef detail page, including tabs for 'Signature Packages', 'Profile', 'Reviews', and 'Availability'.", "dependencies": [1], "details": "Implement the UI/UX for switching between tabs and ensure smooth transitions. This is the container for the different content sections.", "status": "pending"}, {"id": 3, "title": "Profile & Signature Packages Tab Content Development", "description": "Populate the 'Profile' tab with chef's personal information, bio, and general service descriptions, and the 'Signature Packages' tab with details of pre-defined culinary offerings, pricing, and descriptions.", "dependencies": [2], "details": "Integrate data models for chef profiles and signature packages. Design the display of this information within their respective tabs.", "status": "pending"}, {"id": 4, "title": "Reviews Tab Development", "description": "Implement the 'Reviews' tab functionality, allowing display of customer feedback, ratings, and potentially a mechanism for users to submit new reviews.", "dependencies": [2], "details": "Connect to the review data source, design the review display components (e.g., star ratings, comments, user avatars).", "status": "pending"}, {"id": 5, "title": "Availability Tab & Calendar Integration", "description": "Develop the 'Availability' tab, integrating a calendar component that allows chefs to manage their available dates/times and users to view open slots for booking.", "dependencies": [2], "details": "Implement a robust calendar UI, integrate with backend availability data, and ensure real-time updates for booking slots.", "status": "pending"}, {"id": 6, "title": "Implement Distinct Booking Paths", "description": "Create two separate booking initiation flows: one for a basic service request (e.g., custom inquiry) and another for selecting a specific signature package.", "dependencies": [3, 5], "details": "Design the UI/UX for starting a booking from either a general inquiry button or a 'Book Package' button on a specific package. These paths will collect initial booking parameters.", "status": "pending"}, {"id": 7, "title": "Universal Booking Process Integration", "description": "Connect both distinct booking paths (basic service and signature package) to the existing universal booking process, ensuring all necessary data (chef ID, service type, package ID, selected date/time) is passed correctly.", "dependencies": [6], "details": "Map the data collected from the two distinct paths to the input requirements of the universal booking flow, ensuring a seamless transition for the user.", "status": "pending"}]}, {"id": 20, "title": "“私人定制”模式（标准流程）", "description": "开发“私人定制”模式中的标准流程（主题宴席）。实现用户选择主题宴席和用餐标准，填写用餐人数，系统根据人均费用动态计算总价，并接入通用预约流程。", "details": "主题宴席列表页展示不同主题，点击进入详情页。详情页展示按人均收费模式、参考菜单等。预约页使用`u-number-box`选择用餐人数，实时计算总价。", "testStrategy": "测试主题宴席选择和预约流程是否顺畅；验证用餐人数调整后，总价是否正确计算；检查增值服务选择是否正常集成。", "priority": "medium", "dependencies": [8, 10, 11, 13], "status": "pending", "subtasks": []}, {"id": 21, "title": "“私人定制”模式（高端咨询）", "description": "开发“私人定制”模式中的高端流程（国宴/米其林）。实现一个销售线索收集页面，包含宴会类型、预算范围、用餐人数、期望日期、联系人姓名、联系电话、特殊需求描述等表单字段，并提交至后台。", "details": "页面使用`u-form`构建咨询表单，包含各类输入框和选择器。提交按钮点击后，将表单数据通过API发送给后端，并显示提交成功提示。", "testStrategy": "测试咨询表单的填写和提交功能是否正常；验证提交成功提示是否正确显示；检查表单字段的输入校验。", "priority": "medium", "dependencies": [8], "status": "pending", "subtasks": []}, {"id": 22, "title": "“主题宴席”模式列表与详情", "description": "开发“主题宴席”模式的宴席列表页和宴席详情页。列表页展示不同宴席套餐，详情页清晰展示总价/桌、菜品清单、建议单桌人数，并接入通用预约流程。", "details": "宴席列表使用卡片形式展示。详情页展示宴席总价、菜品清单（可分组）。预约页使用步进器选择用餐桌数，并根据单桌餐标计算总价。", "testStrategy": "测试宴席套餐选择和预约流程是否顺畅；验证桌数调整后，总价是否正确计算；检查增值服务选择是否正常集成。", "priority": "medium", "dependencies": [8, 10, 11, 13], "status": "pending", "subtasks": []}, {"id": 23, "title": "订单中心列表页", "description": "开发订单中心列表页面，页面顶部采用Tab栏进行订单状态分类（全部、待付款、待服务、待评价、退款/售后）。列表中每个订单以卡片形式展示关键信息和操作按钮。", "details": "使用`u-tabs`作为顶部Tab导航，每个Tab对应一个订单列表。订单卡片使用自定义组件，根据订单状态动态显示操作按钮（如“去支付”、“去评价”）。", "testStrategy": "测试Tab切换功能是否正常，订单列表是否根据状态正确筛选；验证订单卡片信息展示是否完整，操作按钮是否根据状态动态变化。", "priority": "high", "dependencies": [3, 4], "status": "pending", "subtasks": [{"id": 1, "title": "Define Order Data Models & API Integration", "description": "Establish the data structure for order objects (e.g., ID, status, items, total, dates) and integrate with the backend API to fetch order lists. This includes defining endpoints for different order statuses or a single endpoint with status filtering.", "dependencies": [], "details": "This subtask focuses on the backend communication and data contract.", "status": "pending"}, {"id": 2, "title": "Develop Tabbed Navigation UI Component", "description": "Create the reusable UI component for the tabbed navigation (e.g., 'All Orders', 'Pending', 'Shipped', 'Delivered'). This component will manage tab selection state and provide visual feedback.", "dependencies": [], "details": "Focus on the visual and interactive aspects of the tabs, without connecting to data yet.", "status": "pending"}, {"id": 3, "title": "Implement Base Order Card Display Component", "description": "Develop the core UI component for displaying a single order's details (e.g., order ID, date, total, basic item list). This component should be reusable and accept order data as props.", "dependencies": [], "details": "This subtask focuses on the static layout and display of an individual order.", "status": "pending"}, {"id": 4, "title": "Add Dynamic Action Buttons & Status-Based Rendering to Order Card", "description": "Enhance the Order Card component (from Subtask 3) to conditionally render action buttons (e.g., 'Cancel', 'Track', 'Receive', 'Reorder') and display status-specific information based on the order's current status.", "dependencies": [3], "details": "This subtask adds the dynamic and interactive elements to the order card based on its status.", "status": "pending"}, {"id": 5, "title": "Integrate Data Fetching with Tabs and Order Cards", "description": "Connect the data fetching logic (from Subtask 1) to the tabbed navigation (from Subtask 2) and the Order Card display (from Subtask 4). Implement the logic for switching tabs, triggering appropriate data fetches/filters, and populating the list of Order Cards.", "dependencies": [1, 2, 4], "details": "This subtask brings all components together, managing state and data flow for the entire page.", "status": "pending"}]}, {"id": 24, "title": "订单详情页", "description": "开发订单详情页面，该页面内容和可操作项必须随订单状态实时变化。展示订单状态、服务信息、厨师信息（已分配时）、账单明细、订单信息和动态操作按钮区。", "details": "页面根据订单状态（`待付款`、`待接单`、`待服务`、`服务中`、`待评价`、`已完成`等）条件渲染不同模块和按钮。厨师信息卡片在厨师已分配时显示，并提供“联系厨师”按钮。", "testStrategy": "测试不同订单状态下，详情页内容和操作按钮是否正确显示；验证“联系厨师”按钮功能；检查账单明细是否准确。", "priority": "high", "dependencies": [23], "status": "pending", "subtasks": [{"id": 1, "title": "Setup Base Page Structure & Order Data Fetching", "description": "Create the foundational UI structure for the order detail page and implement the API call to fetch specific order data based on an order ID.", "dependencies": [], "details": "This includes setting up the main layout, loading indicators, and error handling for data retrieval.", "status": "pending"}, {"id": 2, "title": "Define Order Status States & Data Mapping", "description": "Identify all possible order statuses (e.g., pending, confirmed, in-service, completed, cancelled) and define how these statuses will be represented in the fetched order data model.", "dependencies": [], "details": "Establish a clear mapping between backend status codes and frontend display logic.", "status": "pending"}, {"id": 3, "title": "Implement Core Order Detail Display", "description": "Develop UI components to display static and semi-static order details such as service name, service description, chef information (name, rating), and billing summary (total cost, payment status).", "dependencies": [1], "details": "Focus on presenting the essential information clearly, assuming data is available from Subtask 1.", "status": "pending"}, {"id": 4, "title": "Develop Dynamic Content Rendering Logic", "description": "Implement conditional rendering logic to display different sections or information blocks on the page based on the order's current status (e.g., show 'Estimated Arrival' for 'In-Service', hide for 'Completed').", "dependencies": [2, 3], "details": "This involves using the status defined in Subtask 2 to control the visibility and content of various UI elements built in Subtask 3.", "status": "pending"}, {"id": 5, "title": "Implement Conditional Action Buttons", "description": "Develop the logic and UI for displaying context-sensitive action buttons (e.g., 'Contact Chef', 'Confirm Completion', 'Cancel Order') that appear or are enabled/disabled based on the order's status.", "dependencies": [2, 4], "details": "Ensure button actions are correctly wired to backend APIs and their visibility aligns with the order lifecycle.", "status": "pending"}, {"id": 6, "title": "End-to-End Integration & Comprehensive Testing", "description": "Integrate all developed components, ensure seamless data flow, and conduct comprehensive testing across all defined order statuses to validate dynamic content, button visibility, and functionality.", "dependencies": [1, 3, 4, 5], "details": "Perform unit, integration, and user acceptance testing for various order states to ensure robustness and correctness.", "status": "pending"}]}, {"id": 25, "title": "订单搜索功能", "description": "在订单列表页的顶部，提供一个常驻的搜索框。用户输入关键词后，下方的订单列表会实时根据关键词进行筛选，展示匹配的结果。", "details": "使用`u-search`组件作为搜索框。前端根据用户输入，对已加载的订单数据进行本地筛选，或调用后端API进行模糊搜索。提供一键清空搜索框内容的功能。", "testStrategy": "测试搜索框的输入和实时筛选功能是否正常；验证搜索结果的准确性；检查清空按钮是否能恢复列表原始状态。", "priority": "medium", "dependencies": [23], "status": "pending", "subtasks": [{"id": 1, "title": "Integrate u-search Component and Basic Layout", "description": "Add the `u-search` component to the target page or template. Establish the initial HTML structure for the search input field and a designated area for displaying search results.", "dependencies": [], "details": "Ensure the component is correctly imported and rendered. Set up basic styling for visibility.", "status": "pending"}, {"id": 2, "title": "Implement Search Input Event Handling", "description": "Set up event listeners (e.g., `input` or `keyup`) on the search input field to capture user queries. Store the current search query in the component's state or a reactive variable.", "dependencies": [1], "details": "Consider debouncing the input for performance if real-time filtering/API calls are intended.", "status": "pending"}, {"id": 3, "title": "Develop Search Logic (Local Filtering/API Call)", "description": "Implement the core search functionality. This involves either filtering a local dataset based on the current query or making an asynchronous API call to a remote search endpoint. Handle loading states and potential errors during data retrieval.", "dependencies": [2], "details": "Choose between client-side filtering or server-side API call based on data volume and performance requirements. Implement error handling and loading indicators.", "status": "pending"}, {"id": 4, "title": "Display Search Results and Implement Clear Button", "description": "Dynamically render the search results based on the data obtained from the search logic. Implement a 'Clear Search' button that resets the search input field and clears the displayed results.", "dependencies": [3], "details": "Ensure results are displayed clearly and responsively. The clear button should reset the component's state related to the search query and results.", "status": "pending"}]}, {"id": 26, "title": "个人中心主页", "description": "开发个人中心主页，顶部展示用户的微信头像和昵称，并根据用户身份显示VIP标识。页面下方以网格或列表形式，清晰展示各项核心功能入口，如“我的订单”、“地址管理”、“优惠券”等。", "details": "个人信息区使用`u-avatar`和`u-text`展示。功能入口使用`u-grid`或`u-cell-group`布局，每个入口点击后跳转到对应页面。VIP标识根据用户数据条件渲染。", "testStrategy": "测试个人信息展示是否正确；验证所有功能入口是否可点击，并能正确导航到对应页面；检查VIP标识的显示逻辑。", "priority": "high", "dependencies": [3, 5], "status": "pending", "subtasks": [{"id": 1, "title": "Design Personal Center Base Layout", "description": "Outline the main structural components of the personal center page, including header, user info section, navigation area, and a main content display area.", "dependencies": [], "details": "Wireframe the overall page flow and sectioning, defining primary containers and their relationships.", "status": "pending"}, {"id": 2, "title": "Integrate User Avatar & VIP Status Display", "description": "Develop the UI component for displaying the user's avatar, nickname, and conditionally showing VIP status based on user data within the designated user info section.", "dependencies": [1], "details": "Design avatar placeholder, text styles for nickname, and a distinct badge/indicator for VIP status (e.g., 'VIP' text, special icon, or color).", "status": "pending"}, {"id": 3, "title": "Create Core Sub-Module Navigation Links", "description": "Implement clickable navigation links for core sub-modules such as 'My Orders,' 'Address Management,' and 'My Coupons,' ensuring they are clearly visible and accessible within the navigation area.", "dependencies": [1], "details": "Design navigation menu/list, define link styles, and consider icon integration for each module to enhance usability.", "status": "pending"}, {"id": 4, "title": "Apply Basic Styling & Responsiveness", "description": "Apply basic CSS styling to the personal center layout and ensure it is responsive across different screen sizes (desktop, tablet, mobile) for optimal user experience.", "dependencies": [1, 2, 3], "details": "Define a consistent color palette, typography, spacing, and implement media queries for responsive adjustments to layout elements.", "status": "pending"}]}, {"id": 27, "title": "VIP会员中心页面", "description": "开发VIP会员中心页面，从个人中心入口进入。页面顶部展示用户当前的VIP状态，并清晰、吸引人地展示VIP会员的核心权益。接入平台的通用支付模块，完成VIP购买流程。", "details": "页面展示VIP权益列表，可使用`u-list`或自定义卡片。购买VIP按钮点击后，调用通用支付流程。后端更新用户VIP会籍状态和到期日。", "testStrategy": "测试VIP中心页面的信息展示是否准确；验证购买VIP的支付流程是否顺畅，支付成功后VIP状态是否更新。", "priority": "medium", "dependencies": [26, 13], "status": "pending", "subtasks": []}, {"id": 28, "title": "推广员中心页面", "description": "开发推广员中心页面，该页面在用户成功申请推广员后可见。页面显著位置提供专属推广码和一键复制推广链接。展示累计邀请人数、累计订单数、累计佣金和可提现佣金，并接入提现流程。", "details": "推广员中心页面展示推广数据看板。推广码和链接可使用`u-copy`组件实现一键复制。提现按钮点击后，进入提现流程（复用厨师端的提现逻辑）。", "testStrategy": "测试推广员中心页面数据展示是否准确；验证推广码和链接的复制功能；检查提现流程是否正常。", "priority": "medium", "dependencies": [26, 13], "status": "pending", "subtasks": [{"id": 1, "title": "Develop Promoter Data Display", "description": "Implement the user interface and backend integration to fetch and display core promotion data, including total invites, successful orders, and earned commission, on the promoter center page.", "dependencies": [], "details": "Design UI components for data display. Integrate with backend API endpoints (e.g., /api/promoter/stats) to retrieve invite, order, and commission figures. Ensure data is presented clearly.", "status": "pending"}, {"id": 2, "title": "Implement Referral Link Copy Feature", "description": "Add functionality to the promoter center page allowing users to easily copy their unique referral link to the clipboard with a single click.", "dependencies": [1], "details": "Create a 'Copy Link' button or icon. Utilize JavaScript's `navigator.clipboard.writeText()` API. Provide visual feedback (e.g., 'Copied!') upon successful copy.", "status": "pending"}, {"id": 3, "title": "Develop Withdrawal Request Interface", "description": "Create the user interface for initiating a withdrawal request, including input fields for the withdrawal amount and any necessary confirmation steps. Implement client-side validation.", "dependencies": [1], "details": "Design a form or modal for withdrawal requests. Include an input field for the amount. Implement client-side validation to ensure the amount is valid (e.g., positive, within available balance).", "status": "pending"}, {"id": 4, "title": "Integrate Withdrawal API Endpoint", "description": "Connect the withdrawal request UI to the backend API endpoint responsible for processing withdrawal requests, handling success/failure responses, and updating the user's commission balance.", "dependencies": [3], "details": "Integrate with the backend withdrawal API (e.g., POST /api/promoter/withdraw). Implement error handling for API responses. Update the displayed commission balance after a successful withdrawal.", "status": "pending"}]}, {"id": 29, "title": "厨师端项目初始化与登录", "description": "初始化厨师端uni-app项目，实现手机号+短信验证码登录功能。处理厨师首次登录时的引导逻辑，提示其完善资料。", "details": "创建厨师端独立的登录页面。使用`u-input`输入手机号，`u-code`获取验证码。登录成功后，判断厨师资料完善状态，进行相应引导。", "testStrategy": "测试手机号登录和验证码获取功能是否正常；验证新厨师首次登录后的引导流程是否正确触发。", "priority": "high", "dependencies": [1, 4], "status": "pending", "subtasks": []}, {"id": 30, "title": "厨师渐进式资料完善引导", "description": "实现厨师首次登录后的引导流程，将复杂的资料填写任务分解为多个步骤，如个人资料、职业履历、服务信息等，引导厨师逐步完善必填项。", "details": "设计一个引导页面或弹窗，以任务列表形式展示待完善的资料项。每个任务点击后跳转到对应的资料填写页面。在所有必填项完成前，限制厨师端部分核心功能的使用。", "testStrategy": "测试引导流程是否清晰，任务跳转是否正确；验证未完善资料时，核心功能是否被限制；检查资料提交后的状态更新。", "priority": "high", "dependencies": [29], "status": "pending", "subtasks": []}, {"id": 31, "title": "厨师个人资料管理页面", "description": "开发厨师个人资料管理页面，采用多Tab页结构，包括基本信息（形象照、昵称、格言）、职业履历（厨龄、工作履历、健康证、资质证书、荣誉）、服务信息（擅长菜系、基础服务费、增值服务选择、菜品图集）。", "details": "使用`u-tabs`作为页面导航。每个Tab页包含对应的表单组件，如`u-upload`用于图片上传，`u-input`、`u-select`、`u-checkbox-group`等用于信息填写。资料提交后需等待后台审核。", "testStrategy": "测试各Tab页的切换和表单填写功能是否正常；验证图片上传和预览功能；检查必填项校验和提交审核流程。", "priority": "high", "dependencies": [30], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement Multi-Tab UI Structure", "description": "Create the foundational multi-tab layout for the chef's personal profile management page, ensuring smooth navigation between 'Basic Info', 'Professional Resume', and 'Service Info' tabs.", "dependencies": [], "details": "Define tab components, styling, and state management for tab selection. Ensure responsiveness.", "status": "pending"}, {"id": 2, "title": "Develop Basic Information Form", "description": "Build the form for the 'Basic Info' tab, including fields for personal details like name, contact information, and a profile picture placeholder.", "dependencies": [1], "details": "Implement input fields for text, email, phone. Include validation rules. Prepare for profile image upload integration.", "status": "pending"}, {"id": 3, "title": "Develop Professional Resume Form", "description": "Create the form for the 'Professional Resume' tab, allowing chefs to input their certifications, awards, honors, and work experience.", "dependencies": [1], "details": "Design dynamic fields for adding multiple certifications/honors. Include date pickers and text areas for descriptions. Implement validation.", "status": "pending"}, {"id": 4, "title": "Develop Service Information Form", "description": "Construct the form for the 'Service Info' tab, covering cuisine types, pricing/fees, availability, and a gallery section for showcasing dishes.", "dependencies": [1], "details": "Implement multi-select for cuisine types, numerical inputs for fees, and a dedicated section for image uploads for the gallery.", "status": "pending"}, {"id": 5, "title": "Integrate Image Upload Functionality", "description": "Implement robust image upload capabilities for the profile picture (Basic Info) and the service gallery (Service Info), including preview and deletion options.", "dependencies": [2, 4], "details": "Develop front-end image selection and preview. Integrate with a backend storage solution (e.g., cloud storage). Handle file type and size validation.", "status": "pending"}, {"id": 6, "title": "Implement Form Submission and Backend API Integration", "description": "Develop the logic for submitting data from all forms across the tabs to the backend API, including data validation and error handling.", "dependencies": [2, 3, 4, 5], "details": "Create API endpoints for profile data submission. Implement data serialization and deserialization. Handle success/error responses and user feedback.", "status": "pending"}]}, {"id": 32, "title": "厨师实名认证集成", "description": "集成第三方实名认证服务（如腾讯云E-KYC），实现厨师的人脸识别和身份证号码验证流程。在厨师端界面上更新显示“已认证”徽章。", "details": "在资料完善流程中引导厨师进行实名认证。调用微信小程序提供的第三方认证接口或跳转到认证小程序。后端接收认证结果并更新厨师的`is_verified`状态。", "testStrategy": "测试实名认证流程是否能正确跳转和返回结果；验证认证成功后，厨师端是否显示“已认证”徽章；检查认证失败时的重试机制。", "priority": "high", "dependencies": [30], "status": "pending", "subtasks": [{"id": 1, "title": "Configure Third-Party KYC Service Integration", "description": "Set up API keys, credentials, and integrate the Tencent Cloud E-KYC SDK/API into both frontend and backend environments. This includes initial configuration, network setup, and ensuring secure communication channels.", "dependencies": [], "details": "Establish secure connection, configure authentication, and initialize SDKs.", "status": "pending"}, {"id": 2, "title": "Implement Client-Side Face Recognition & ID Verification Flow", "description": "Develop the user interface and logic for initiating the KYC process, guiding the user through face recognition and ID document scanning using the integrated third-party service's client-side components.", "dependencies": [1], "details": "Design user flow, integrate camera access, handle real-time feedback from KYC service.", "status": "pending"}, {"id": 3, "title": "Develop Backend Endpoint for KYC Result Handling", "description": "Create a secure backend endpoint to receive and process the authentication results (success/failure, verification data) from the third-party KYC service, including handling webhooks or polling for status updates.", "dependencies": [1], "details": "Implement callback listener, parse verification data, validate authenticity of results.", "status": "pending"}, {"id": 4, "title": "Update Frontend UI with Verification Status", "description": "Implement logic to update the user interface, specifically displaying a 'verified' badge or appropriate success/failure messages, based on the authentication results received from the backend.", "dependencies": [3], "details": "Display 'verified' badge, show error messages for failures, provide user feedback.", "status": "pending"}, {"id": 5, "title": "Securely Store Verification Status & Audit Logs", "description": "Persist the final KYC verification status (e.g., 'verified', 'failed', 'pending') in the database for the user, and implement comprehensive logging for audit trails and compliance purposes.", "dependencies": [3], "details": "Update user profile with verification status, log all KYC attempts and outcomes, ensure data privacy compliance.", "status": "pending"}]}, {"id": 33, "title": "厨师个人套餐创建与管理", "description": "开发厨师创建、编辑、上架/下架、删除个人套餐的功能。套餐创建表单包含套餐主图、名称、价格、适用人数、菜品数量和动态菜品清单管理。", "details": "套餐列表页展示所有套餐卡片，提供操作按钮。创建/编辑页使用`u-form`构建表单，菜品清单使用可动态增删的列表组件。套餐提交后状态为`待审核`。", "testStrategy": "测试套餐的增删改查功能是否正常；验证套餐上架/下架状态切换；检查提交审核后，状态是否正确更新。", "priority": "high", "dependencies": [31], "status": "pending", "subtasks": [{"id": 1, "title": "Develop Backend API for Chef Package Management", "description": "Design and implement RESTful APIs for full CRUD operations on chef packages, including nested dish lists, image storage integration, and package status updates (on/off shelf, pending review).", "dependencies": [], "details": "Define data models for Package, Dish, and Image. Implement endpoints for creating, reading, updating, and deleting packages. Include endpoints for managing dishes within a package and handling image uploads/retrieval.", "status": "pending"}, {"id": 2, "title": "Implement Chef Package List Display", "description": "Create the user interface to display a list of all chef packages with key details (name, status, number of dishes, main image). Include filtering and sorting capabilities.", "dependencies": [1], "details": "Develop frontend components to fetch and render package data from the backend API. Design the layout for package cards/rows.", "status": "pending"}, {"id": 3, "title": "Build Create/Edit Chef Package Form", "description": "Develop a comprehensive form for chefs to create new packages or edit existing ones. This form will include fields for package name, description, price, and placeholders for dynamic dish lists and image uploads.", "dependencies": [1], "details": "Design form fields, validation rules, and submission logic. Ensure the form can pre-populate data for editing existing packages.", "status": "pending"}, {"id": 4, "title": "Integrate Dynamic Dish List Management into Form", "description": "Implement the functionality within the Create/Edit Package Form to dynamically add, edit, reorder, and remove dishes associated with a package. Each dish should have fields like name, description, and price.", "dependencies": [1, 3], "details": "Develop UI components for managing dish items (e.g., 'Add Dish' button, editable rows, drag-and-drop reordering). Ensure data is correctly structured for backend submission.", "status": "pending"}, {"id": 5, "title": "Implement Image Upload Functionality", "description": "Integrate image upload capabilities into the Create/Edit Package Form, allowing chefs to upload a main image for their package. This includes client-side validation and secure storage integration.", "dependencies": [1, 3], "details": "Develop UI for image selection and preview. Implement logic for uploading images to the backend and associating them with the correct package. Handle image resizing/optimization if necessary.", "status": "pending"}, {"id": 6, "title": "Develop Chef Package Status Management", "description": "Implement the UI and logic for managing package statuses (e.g., 'On Shelf', 'Off Shelf', 'Pending Review'). This includes displaying the current status and providing controls for chefs to change it where applicable.", "dependencies": [1, 2], "details": "Add status indicators to the package list and/or detail view. Implement buttons/dropdowns for status changes, ensuring proper permissions and backend calls.", "status": "pending"}]}, {"id": 34, "title": "厨师工作台（订单处理）", "description": "实现厨师工作台仪表盘，醒目显示待接单、待服务、服务中订单计数。下方以列表或卡片形式展示这些订单，包含服务时间、地址、类型、收入和主要操作按钮（如“接单”）。", "details": "工作台使用`u-grid`或自定义布局展示订单计数。订单列表使用`u-list`或`u-card`展示。根据订单状态动态显示“接单”、“查看详情”等按钮。实现接单倒计时功能。", "testStrategy": "测试工作台订单计数是否准确；验证订单列表加载和展示是否正常；检查“接单”和“拒绝”操作是否生效，订单状态是否正确流转。", "priority": "high", "dependencies": [29, 4], "status": "pending", "subtasks": [{"id": 1, "title": "Core Workbench UI Development", "description": "Develop the foundational user interface for the chef workbench, including the dashboard area for displaying real-time order counts (e.g., total, pending, completed) and the layout for the pending order list.", "dependencies": [], "details": "Wireframe and implement the basic HTML/CSS structure for the dashboard and order list. Display static placeholder data initially for counts and list items.", "status": "pending"}, {"id": 2, "title": "Pending Order List Data Integration", "description": "Integrate backend data to populate the pending order list dynamically, displaying essential order details such as order ID, items, customer name, and timestamp.", "dependencies": [1], "details": "Fetch pending order data from a mock API or a temporary data source and render it within the list component. Ensure proper data binding and display of all required order attributes.", "status": "pending"}, {"id": 3, "title": "Dynamic Order Action Buttons Implementation", "description": "Implement dynamic 'Accept' and 'Reject' action buttons for each order in the pending list. These buttons should be visually distinct and responsive.", "dependencies": [2], "details": "Add 'Accept' and 'Reject' buttons to each order item in the list. Define initial button states and basic click handlers (without backend integration yet).", "status": "pending"}, {"id": 4, "title": "Order Countdown Timer Integration", "description": "Implement a real-time countdown timer for each pending order, indicating the time remaining for the chef to accept or reject the order.", "dependencies": [2], "details": "For each order, calculate and display a countdown based on a predefined time limit. Update the timer every second and consider visual cues for nearing expiration.", "status": "pending"}, {"id": 5, "title": "Order Status Update API & UI Integration", "description": "Develop the backend API endpoint for updating order statuses (accept/reject) and integrate this API with the frontend action buttons, ensuring successful status transitions and UI updates.", "dependencies": [3, 4], "details": "Create a REST API endpoint (e.g., `/api/orders/{id}/status`) that accepts 'accepted' or 'rejected' status. Connect the 'Accept'/'Reject' buttons to this API. Handle success/failure responses and update the UI (e.g., remove order from pending list, show confirmation).", "status": "pending"}]}, {"id": 35, "title": "厨师订单历史与操作", "description": "开发厨师订单历史页面，提供Tab式导航按历史状态筛选（全部、已完成、已评价、已取消），并支持按月份进行筛选或搜索。在订单详情页实现“联系客户”和“确认完成”操作。", "details": "订单历史页使用`u-tabs`和`u-list`。订单详情页的“联系客户”按钮可拉起电话拨号或通过平台客服转接。“确认完成”按钮点击后，更新订单状态为`待评价`。", "testStrategy": "测试订单历史列表的筛选和搜索功能；验证“联系客户”功能是否正常；检查“确认完成”操作后，订单状态是否正确更新并触发结算流程。", "priority": "high", "dependencies": [34], "status": "pending", "subtasks": [{"id": 1, "title": "Backend API & Database Schema for Orders", "description": "Design and implement the database schema for orders, including status, customer info, chef info, and service details. Develop API endpoints for fetching order lists and individual order details.", "dependencies": [], "details": "This foundational step ensures data availability for both customer and chef interfaces.", "status": "pending"}, {"id": 2, "title": "Develop Order History Page (Customer View)", "description": "Create the front-end UI for the customer's order history page, displaying a list of their past and current orders. Focus on basic layout and data display.", "dependencies": [1], "details": "This involves fetching and rendering order data from the backend API.", "status": "pending"}, {"id": 3, "title": "Implement Filtering for Order History Page", "description": "Add tabbed filtering by order status (e.g., 'Pending', 'Completed', 'Cancelled') and date range/month filtering to the customer's order history page.", "dependencies": [2], "details": "This enhances usability by allowing customers to easily navigate their order history.", "status": "pending"}, {"id": 4, "title": "Develop Chef's Order Detail Page", "description": "Create the front-end UI for the chef's detailed view of a specific order, displaying all relevant order information, customer details, and service specifics.", "dependencies": [1], "details": "This page is crucial for chefs to manage and understand individual orders.", "status": "pending"}, {"id": 5, "title": "Implement 'Contact Customer' Functionality", "description": "Add buttons/links on the chef's order detail page to initiate contact with the customer via phone call and/or in-app chat.", "dependencies": [4], "details": "This enables direct communication between chefs and customers for order-related queries.", "status": "pending"}, {"id": 6, "title": "Implement 'Confirm Completion' Action", "description": "Develop the functionality on the chef's order detail page for the chef to mark an order as 'Completed', including updating the order status via API.", "dependencies": [4], "details": "This action is critical for updating order status and triggering subsequent processes like payment or reviews.", "status": "pending"}]}, {"id": 36, "title": "厨师接单状态与服务时间管理", "description": "在厨师端“我的”主页顶部提供一个醒目的全局开关，允许厨师在“接单中”和“休息中”两个状态之间实时切换。同时，开发一个全屏日历视图，供厨师管理未来特定日期或时间段的可预约状态。", "details": "全局开关使用`u-switch`组件。服务时间管理使用自定义日历组件，厨师可点击日期切换状态。这些状态将同步到用户端，影响厨师的可见性和可预约性。", "testStrategy": "测试接单状态切换功能是否正常，并验证用户端厨师列表的显示变化；检查服务时间日历的设置和显示是否准确，是否能正确锁定厨师档期。", "priority": "high", "dependencies": [29], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Global On/Off Duty Toggle", "description": "Develop the user interface and frontend logic for a global switch that allows a chef to set their overall availability status (on-duty/off-duty). This toggle should control the chef's general visibility.", "dependencies": [], "details": "This includes UI component development, state management for the toggle, and initial frontend event handling.", "status": "pending"}, {"id": 2, "title": "Develop Service Time Calendar View", "description": "Create an interactive calendar interface where chefs can visualize and manage their specific service time slots. This view should reflect the global on/off duty status.", "dependencies": [1], "details": "Focus on calendar rendering, navigation (month/week view), and basic display of available/unavailable days.", "status": "pending"}, {"id": 3, "title": "Implement Date/Time Slot Selection Logic", "description": "Develop the frontend logic for selecting, adding, editing, and deleting specific date and time slots within the service time calendar view. This includes handling overlapping slots and validation.", "dependencies": [2], "details": "This involves modal/form for slot creation/editing, validation rules, and local state updates for selected slots.", "status": "pending"}, {"id": 4, "title": "Design and Implement Backend API for Availability Management", "description": "Create robust backend API endpoints to store, retrieve, update, and delete chef availability data, including the global on/off status and specific service time slots.", "dependencies": [], "details": "Define data models for chef availability, implement CRUD operations, and ensure secure access to endpoints.", "status": "pending"}, {"id": 5, "title": "Implement Frontend-Backend Synchronization", "description": "Establish real-time or near real-time synchronization between the frontend UI (global toggle, calendar, time slots) and the backend API to ensure availability data is consistent, persistent, and accurately reflected for users.", "dependencies": [1, 3, 4], "details": "Implement API calls from frontend components, handle responses, error handling, and update UI based on backend data. Consider optimistic updates or loading states.", "status": "pending"}]}, {"id": 37, "title": "厨师服务区域与评价管理", "description": "开发厨师服务区域变更申请页面，厨师可从预设区域列表中选择新的目标服务区域并提交申请。同时，开发厨师评价管理页面，展示所有收到的用户评价列表。", "details": "服务区域申请页使用`u-picker`或`u-select`选择区域，提交后等待后台审核。评价管理页使用`u-list`展示评价卡片，包含星级评分、评论和关联订单信息。", "testStrategy": "测试服务区域变更申请的提交流程；验证评价列表的加载和展示是否正常，信息是否完整。", "priority": "medium", "dependencies": [29], "status": "pending", "subtasks": []}, {"id": 38, "title": "厨师财务管理", "description": "开发厨师财务管理页面，醒目展示总收入和可提现金额。实现提现流程，包括首次使用时绑定银行账户，输入提现金额，并清晰展示服务费/佣金和实际到账金额。开发收支明细和提现记录页面。", "details": "财务总览使用数据卡片展示。提现流程包含银行卡绑定表单和提现金额输入框。收支明细和提现记录使用`u-list`展示，数据从后端获取。", "testStrategy": "测试财务数据展示是否准确；验证提现流程是否顺畅，银行卡绑定和提现申请是否成功；检查收支明细和提现记录是否完整且正确。", "priority": "high", "dependencies": [29, 4], "status": "pending", "subtasks": [{"id": 1, "title": "Financial API Integration & Core Data Model Definition", "description": "Establish secure connections with necessary financial APIs (e.g., for transaction processing, bank account validation) and define the core database schema for storing user financial data, income, expenses, and withdrawal records.", "dependencies": [], "details": "Research and select appropriate financial APIs. Implement secure authentication and data encryption for API communication. Design database tables for users' financial profiles, income streams, expense categories, and withdrawal transactions.", "status": "pending"}, {"id": 2, "title": "Financial Overview Page Development (Income & Withdrawable Balance)", "description": "Develop the main financial overview page displaying the user's total income and current withdrawable balance. This page will serve as the dashboard for financial activities.", "dependencies": [1], "details": "Design UI/UX for the overview page. Implement backend logic to aggregate income and calculate withdrawable balance from the defined data model. Ensure real-time or near real-time data display.", "status": "pending"}, {"id": 3, "title": "Bank Account Binding Form Implementation", "description": "Create a secure form and backend process for users to bind their bank accounts for withdrawals. This includes validation and secure storage of bank details.", "dependencies": [1], "details": "Develop a multi-step form for bank account details (e.g., account number, routing number, bank name). Implement validation using financial APIs. Ensure PCI DSS compliance for sensitive data handling and encryption at rest.", "status": "pending"}, {"id": 4, "title": "Withdrawal Request Form Development (with <PERSON><PERSON>)", "description": "Implement the withdrawal request form, allowing users to specify an amount to withdraw and clearly displaying any associated transaction fees before submission.", "dependencies": [2, 3], "details": "Design the withdrawal form UI. Implement logic to check available withdrawable balance. Integrate fee calculation logic (fixed, percentage, or tiered) and display it dynamically. Connect to financial APIs for initiating withdrawal requests.", "status": "pending"}, {"id": 5, "title": "Income/Expense Details Page Development", "description": "Create dedicated pages or sections to display detailed breakdowns of income and expense transactions, allowing users to view historical financial activities.", "dependencies": [1, 2], "details": "Design UI for detailed transaction lists. Implement filtering, sorting, and pagination for large datasets. Ensure accurate categorization and display of individual income and expense entries.", "status": "pending"}, {"id": 6, "title": "Withdrawal History Page Development", "description": "Develop a dedicated page to display a comprehensive history of all withdrawal requests, including their status (e.g., pending, processed, failed) and relevant timestamps.", "dependencies": [1, 4], "details": "Design UI for the withdrawal history table. Implement backend logic to retrieve and display all past withdrawal requests. Include status updates and error messages for failed transactions.", "status": "pending"}, {"id": 7, "title": "Comprehensive Security Audit, Testing & Deployment Preparation", "description": "Conduct a thorough security audit of all financial modules, perform extensive functional and integration testing, and prepare for production deployment.", "dependencies": [1, 2, 3, 4, 5, 6], "details": "Perform penetration testing, vulnerability scanning, and code reviews for financial features. Conduct unit, integration, and end-to-end testing. Prepare deployment scripts, monitoring tools, and rollback plans.", "status": "pending"}]}, {"id": 39, "title": "厨师营销活动页面", "description": "开发厨师端营销活动页面，展示平台发布的各类活动信息，并提供“立即报名”按钮，引导厨师参与活动以获得更多曝光和订单。", "details": "活动页面使用`u-card`或自定义布局展示活动详情，包含活动图片、标题、描述和报名按钮。报名按钮点击后，调用后端API提交报名信息。", "testStrategy": "测试营销活动页面加载和展示是否正常；验证“立即报名”按钮点击后，报名信息是否成功提交。", "priority": "low", "dependencies": [29], "status": "pending", "subtasks": []}]}