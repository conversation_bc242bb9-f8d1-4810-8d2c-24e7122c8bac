# Task ID: 2
# Title: 全局样式与主题定义
# Status: done
# Dependencies: 1
# Priority: high
# Description: 基于UnoCSS的原子化CSS能力和uView Plus的主题配置，定义项目全局的颜色、字体、间距等样式变量。确保所有界面布局以iPhone 16 Pro的屏幕尺寸为基准，并具备良好的响应式表现。
# Details:
在`uno.config.ts`中配置UnoCSS的预设和自定义规则，定义常用颜色变量。利用uView Plus的主题定制功能，统一组件样式。使用`rem`或`vw/vh`等相对单位进行布局，确保在不同设备上的适配性。

# Test Strategy:
在不同尺寸的模拟器（如iPhone 16 Pro, Android主流机型）上测试页面布局和样式是否一致且美观；验证全局颜色和字体是否按设计稿呈现。
