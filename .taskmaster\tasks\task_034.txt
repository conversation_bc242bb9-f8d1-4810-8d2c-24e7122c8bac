# Task ID: 34
# Title: 厨师工作台（订单处理）
# Status: pending
# Dependencies: 29, 4
# Priority: high
# Description: 实现厨师工作台仪表盘，醒目显示待接单、待服务、服务中订单计数。下方以列表或卡片形式展示这些订单，包含服务时间、地址、类型、收入和主要操作按钮（如“接单”）。
# Details:
工作台使用`u-grid`或自定义布局展示订单计数。订单列表使用`u-list`或`u-card`展示。根据订单状态动态显示“接单”、“查看详情”等按钮。实现接单倒计时功能。

# Test Strategy:
测试工作台订单计数是否准确；验证订单列表加载和展示是否正常；检查“接单”和“拒绝”操作是否生效，订单状态是否正确流转。

# Subtasks:
## 1. Core Workbench UI Development [pending]
### Dependencies: None
### Description: Develop the foundational user interface for the chef workbench, including the dashboard area for displaying real-time order counts (e.g., total, pending, completed) and the layout for the pending order list.
### Details:
Wireframe and implement the basic HTML/CSS structure for the dashboard and order list. Display static placeholder data initially for counts and list items.

## 2. Pending Order List Data Integration [pending]
### Dependencies: 34.1
### Description: Integrate backend data to populate the pending order list dynamically, displaying essential order details such as order ID, items, customer name, and timestamp.
### Details:
Fetch pending order data from a mock API or a temporary data source and render it within the list component. Ensure proper data binding and display of all required order attributes.

## 3. Dynamic Order Action Buttons Implementation [pending]
### Dependencies: 34.2
### Description: Implement dynamic 'Accept' and 'Reject' action buttons for each order in the pending list. These buttons should be visually distinct and responsive.
### Details:
Add 'Accept' and 'Reject' buttons to each order item in the list. Define initial button states and basic click handlers (without backend integration yet).

## 4. Order Countdown Timer Integration [pending]
### Dependencies: 34.2
### Description: Implement a real-time countdown timer for each pending order, indicating the time remaining for the chef to accept or reject the order.
### Details:
For each order, calculate and display a countdown based on a predefined time limit. Update the timer every second and consider visual cues for nearing expiration.

## 5. Order Status Update API & UI Integration [pending]
### Dependencies: 34.3, 34.4
### Description: Develop the backend API endpoint for updating order statuses (accept/reject) and integrate this API with the frontend action buttons, ensuring successful status transitions and UI updates.
### Details:
Create a REST API endpoint (e.g., `/api/orders/{id}/status`) that accepts 'accepted' or 'rejected' status. Connect the 'Accept'/'Reject' buttons to this API. Handle success/failure responses and update the UI (e.g., remove order from pending list, show confirmation).

