<template>
  <view>
    <u-navbar
      title="订单"
      :autoBack="false"
      bgColor="#ffffff"
      titleStyle="color: #000000; font-size: 32rpx; font-weight: 500;"
      :fixed="true"
      :safeAreaInsetTop="true"
    ></u-navbar>
    <view class="content">
      <text>订单页面</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  }
};
</script>

<style scoped>
.content {
  padding: 20rpx;
  margin-top: 90rpx; /* 为固定导航栏留出空间 */
}
</style> 