# 私厨上门服务平台产品需求文档 (PRD)
---
### 1. 引言 (Introduction)
#### 1.1. 项目背景
随着生活节奏的加快和消费升级，家庭用餐场景出现了新的需求。一方面，城市家庭（尤其是年轻一代和中产家庭）希望在特殊场合（如家庭聚会、朋友宴请、节日庆典）享受高质量、个性化的餐饮体验，但又受限于自身厨艺、时间或精力。另一方面，社会上存在大量拥有专业烹饪技能的厨师，他们希望利用自己的手艺获得更灵活、更多元的收入来源。
传统的餐饮模式（如餐厅、外卖）难以完全满足这种即时、个性化、场景化的上门服务需求。"私厨上门服务平台"应运而生，旨在通过互联网技术，高效地连接有高质量餐饮需求的家庭和有专业技能的厨师，提供一个便捷、安全、标准化的在线服务交易市场。
#### 1.2. 项目目标
本项目旨在打造一个连接用户与专业厨师的综合性上门服务平台，核心目标如下：
-   **用户端**: 为用户提供一个可以方便快捷地发现、预约和评价私厨服务的在线入口，满足其在不同场景下的个性化餐饮需求，提升生活品质。
-   **厨师端**: 为厨师提供一个灵活的线上执业平台，帮助他们将烹饪技能产品化，通过自主管理服务、时间、价格和个人品牌来获取收益，实现个人价值。
-   **平台**: 构建一个高效、可靠、安全的双边市场。通过标准化的服务流程、安全的支付保障、透明的信誉体系和精细化的运营管理，确保用户体验和厨师权益，最终实现平台的商业价值。
#### 1.3. 项目范围
本项目需要开发三个核心终端，以支撑完整的业务闭环：
1.  **用户端(小程序)**: 面向最终消费者，提供服务浏览、厨师发现、在线预约、支付、订单管理和评价等功能。
2.  **厨师端(小程序)**: 面向服务提供者（厨师），提供个人资料管理、服务（套餐）发布、服务时间管理、订单处理、财务管理（账单与提现）等功能。
3.  **后台管理系统 (Web/小程序)**: 面向平台运营团队，提供厨师管理、订单管理、内容与产品管理、财务管理、系统管理等全面的后台运营功能。
#### 1.4. 目标用户
-   **核心用户 (C端)**:
    -   追求生活品质，注重家庭生活仪式感。
    -   社交活跃，有家庭聚会、朋友宴请的需求。
    -   对新鲜事物接受度高，习惯使用线上服务。
-   **核心服务提供者 (B端)**:
    -   拥有专业烹饪技能的厨师。
    -   希望获得主业之外的额外收入，或寻求更自由的执业方式。
    -   具备一定的服务意识和沟通能力。
-   **平台运营人员**:
    -   调度员、运营经理、财务人员、系统管理员等，负责平台的日常运营和管理。
---
### 2. 总体业务流程![mermaid-2025616 134718.png](http://home.128228.xyz:5543/obsidian/20250616134819762.png)
---
### 3. 功能需求 (Functional Requirements)
#### 3.1. 用户端小程序 (User Mini Program)
##### 3.1.1. 核心服务与首页 (Core Services & Homepage)
-   **FR1.1 - 首页布局**: 首页需采用模块化设计，从上至下依次为：顶部搜索栏、轮播广告位、核心服务入口矩阵（金刚区）、推荐内容模块（如明星厨师、热门套餐）、底部导航栏。
-   **FR1.2 - 地理位置服务**:
    -   应用启动时，需弹出隐私协议，用户同意后请求获取地理位置权限。
    -   首页需展示用户当前定位，并允许用户手动切换城市或地址。
-   **FR1.3 - 搜索功能**: 提供全局搜索框，支持用户按厨师名称、菜系、套餐名称等关键词进行搜索。
-   **FR1.4 - 轮播广告**: 首页需包含一个可由后台配置的轮播图组件，用于展示平台活动、推广特定服务或厨师。
-   **FR1.5 - 核心服务入口**:
    -   以网格菜单形式，清晰展示平台提供的所有服务模式。
    -   至少包含："一口价加工"、"精选套餐"、"单点厨师"、"私人定制"、"主题宴席"。
-   **FR1.6 - 内容推荐**:
    -   **明星厨师推荐**: 以横向滚动列表形式，展示由后台推荐的厨师。卡片需包含厨师照片、姓名、评分、厨龄、基础服务费和个人格言。
    -   **热门套餐推荐**: 以图文卡片形式，展示由后台推荐的套餐。卡片需包含套餐图片、名称、价格、适用人数、菜品数量及包含的服务项。
-   **FR1.7 - 底部导航**: 包含"首页"、"订单"、"我的"等核心页面入口。
##### 3.1.2. 用户认证与登录 (Authentication & Login)
-   **FR2.1 - 微信一键登录 (USER-AUTH-001)**
    -   **用户故事:** 作为一名新用户或老用户，我希望能通过微信授权，快速、安全地完成注册或登录，无需记忆复杂的账号密码，从而可以立即开始使用平台的核心功能。
    -   **前置条件:**
        -   用户设备已安装微信且处于登录状态。
        -   用户首次打开小程序或处于未登录状态。
    -   **核心流程:**
        1.  用户进入小程序，系统检测到用户未登录，自动或在用户触发操作（如点击"我的"、"下单"）后，弹出登录授权窗口。
        2.  授权窗口清晰展示平台名称、Logo，并包含一个醒目的"微信授权登录"按钮，以及服务协议和隐私政策的勾选项和链接。
        3.  用户点击"微信授权登录"按钮，小程序调用微信官方接口，请求获取用户的微信绑定手机号。
        4.  用户在微信弹出的官方授权界面点击"允许"。
        5.  系统获取到加密的手机号信息，并在后端解密，以此手机号作为唯一身份标识。
        6.  **后端逻辑判断:**
            -   **手机号已存在 (老用户):** 系统直接使用该手机号登录，更新用户的微信昵称、头像等信息，并返回登录成功状态。
            -   **手机号不存在 (新用户):** 系统使用该手机号自动创建一个新账户，并将用户的微信昵称、头像等信息存入，完成静默注册，并返回登录成功状态。
    -   **数据要素:**
        -   **输入:** 微信授权Code (用于换取手机号和OpenID)。
        -   **处理:** 后端通过Code换取用户信息，进行用户匹配或创建，生成并返回自定义登录态Token。
        -   **输出:** 登录态Token，用户信息（用户ID、昵称、头像、会员等级等）。
    -   **业务规则:**
        -   一个手机号只能对应一个平台账户。
        -   必须在用户授权前展示并要求用户同意服务协议和隐私政策。
        -   获取到的微信用户信息（昵称、头像）应自动同步更新到平台数据库。
    -   **成功结果:** 用户无感知或通过一次点击完成注册/登录，并停留在触发登录前的页面或跳转至小程序首页。
    -   **异常处理:**
        -   **用户拒绝授权:** 停留在当前页，并友好提示"授权登录后才能体验完整服务"，保留重新授权的入口。
        -   **获取手机号失败:** 提示"授权失败，请稍后重试"。
        -   **网络/服务器异常:** 友好提示"网络开小差了，请检查网络后重试"。
-   **FR2.2 - 隐私协议 (USER-AUTH-002)**
    -   **用户故事:** 作为一个关心个人隐私的用户，我希望在授权任何个人信息（如手机号、位置）之前，能够清楚地看到平台的隐私政策和服务协议，并自主选择是否同意。
    -   **核心流程:**
        1.  在任何需要用户授权敏感信息的场景（如首次登录、选择地址），系统必须以弹窗或页面的形式，主动展示服务协议和隐私政策的摘要或链接。
        2.  必须提供一个明确的勾选框，默认为未勾选或已勾选（取决于法规要求，建议默认为已勾选但可取消）。
        3.  用户必须勾选"同意"后，相关的授权按钮（如"微信授权登录"）才能变为可点击状态。
        4.  协议和服务条款的链接必须可以点击，并跳转到内容详情页，内容页应清晰、易读。
    -   **业务规则:**
        -   用户同意协议的版本号应被记录。
        -   当协议有重大更新时，需要重新提醒用户并获得其同意。
##### 3.1.3. 服务模式与流程 (Service Models & Flows)
-   **FR3.1 - "一口价加工"模式 (USER-FLOW-001)**:
    -   **用户故事:** 作为一名不擅长烹饪但已准备好食材的用户，我希望能以一个固定的、透明的价格，快速预订一位专业的厨师上门为我完成烹饪，解决我的用餐需求，而无需纠结于挑选厨师。
    -   **规则**: 用户自备食材，平台派单，用户不可指定厨师。
    -   **核心流程与界面元素:**
        1.  用户在首页点击"一口价加工"入口，进入服务介绍及套餐选择页面。
        2.  页面顶部清晰展示此模式的核心规则："您提供食材，我们提供专业厨师上门烹饪"。
        3.  以卡片形式展示不同规格的加工套餐，例如：
            -   **卡片一:** "四菜一汤" | ¥199 | 建议2-3人
            -   **卡片二:** "六菜一汤" | ¥299 | 建议4-5人
            -   **卡片三:** "八菜两汤" | ¥399 | 建议6-8人
        4.  用户选择一个基础套餐后，进入预约信息填写页面。
        5.  **预约页面:**
            -   **核心信息:**
                -   **服务地址:** 支持从地址列表选择、手动新增或使用当前定位。
                -   **用餐时间:** 以日期供用户选择。可选时间需基于后台配置的可服务时间。
            -   **菜品数量调整:**
                -   默认显示所选套餐的菜品数量（如"8道"）。
                -   提供步进器(加/减号)，允许用户额外增加加工菜品的数量（如增加到"10道"）。
                -   每增加一道菜，系统需根据后台配置的`单菜加价`规则，实时更新总价。
            -   **增值服务:** (详见FR4.1) 提供可勾选的增值服务列表（如租用厨具、需要清洁等）。
            -   **账单明细:** 页面底部实时展示费用构成：基础服务费 + 加菜费 + 增值服务费 = 订单总额。
    -   **数据要素:**
        -   **输入:** 服务地址、服务时间、基础套餐选择、增加的菜品数量、所选增值服务。
        -   **处理:** 根据定价规则动态计算总费用。
        -   **输出:** 生成待支付的订单信息。
    -   **业务规则:**
        -   此模式下的厨师由平台根据手动指派，用户端不展示厨师选择环节。
        -   不同区域可以配置不同的基础服务费和加菜费。
        -   可预约的时间段由后台统一配置。
    -   **后续流程:** 用户确认信息后，进入通用支付流程 (FR4.x)。支付成功后，订单状态变为`待接单`，由后台系统进行派单。
-   **FR3.2 - "精选套餐"模式 (USER-FLOW-002)**:
    -   **用户故事:** 为了筹备一次家庭聚餐，我希望能从平台预先搭配好的、图文并茂的套餐中直接选择，省去自己点菜和准备食材的麻烦，享受一站式的便捷服务。
    -   **规则**: 平台提供食材和厨师，平台派单。
    -   **核心流程与界面元素:**
        1.  用户点击"精选套餐"入口，进入套餐列表页。
        2.  套餐列表以精美的图文卡片形式展示，卡片信息包含：套餐主图、名称（如"阖家欢乐六人餐"）、价格、适用人数、菜品数量。
        3.  用户点击任一套餐卡片，进入套餐详情页。
        4.  **套餐详情页:**
            -   **顶部:** 套餐主图、名称、价格、简介。
            -   **菜品清单:** 详细列出套餐内包含的所有菜品，可按"凉菜"、"热菜"、"汤羹"、"主食"等类别进行分组展示。
            -   **服务说明:** 清晰告知"套餐内菜品可与厨师协商微调"、"价格已包含食材及服务费"等规则。
        5.  用户点击"立即预订"后，进入预约信息填写页面。
        6.  **预约页面:**
            -   套餐内容和价格在此页面为固定信息，不可修改。
            -   用户需选择服务地址和用餐时间。
            -   提供可选的增值服务模块。
    -   **数据要素:**
        -   **输入:** 服务地址、服务时间、所选增值服务。
        -   **处理:** 汇总订单信息。
        -   **输出:** 生成待支付的订单信息。
    -   **业务规则:**
        -   套餐内的菜品、价格由平台在后台统一配置和管理。
        -   与"一口价加工"模式类似，厨师由平台根据算法自动指派。
    -   **后续流程:** 用户确认信息后，进入通用支付流程 (FR4.x)。
-   **FR3.3 - "单点厨师"模式 (USER-FLOW-003)**:
    -   **用户故事:** 我对菜品有特定的偏好，或对某位厨师的风格特别欣赏，我希望能直接选择心仪的厨师，并根据他的服务标准和可预约时间来安排我的宴请。
    -   **规则**: 用户可自由选择厨师，按厨师的收费标准付费。
    -   **核心流程与界面元素:**
        1.  用户点击"单点厨师"入口，进入厨师列表页面。
        2.  **厨师列表页 (USER-FINDCHEF-003):**
            -   **筛选与排序:**
                -   顶部提供按`菜系`（川菜、粤菜等）的快速筛选Tab。
                -   提供综合排序功能，包含`综合推荐`、`销量最高`、`评价最好`等选项。
                -   提供"筛选"按钮，点击后弹出侧边栏或页面，支持按`厨师等级`、`服务费范围`、`其他服务`（如可否带服务员）进行多维度组合筛选。
            -   **厨师卡片:**
                -   清晰展示厨师的姓名、专业照片、星级评分。
                -   明确标示其基础`服务费`（如"¥599/次"，并注明"含8菜1汤"）。
                -   展示核心标签，如`厨龄15年`、`川菜大师`、以及`可接单`/`休息中`等实时状态。
        3.  用户点击厨师卡片，进入厨师详情页。
        4.  **厨师详情页 (USER-FINDCHEF-004):**
            -   **页面顶部:** 固定展示厨师的核心信息：头像、姓名、星级评分、基础服务费（如 "¥599/次，含8菜1汤"），并提供一个主要的`预约厨师`按钮，对应其基础服务。
            -   **Tab式内容区:**
                -   **Tab 1: 招牌套餐 (默认)**:
                    -   **用户故事**: 作为用户，我想看看这位厨师有没有自己搭配好的、有特色的套餐可以直接预订。
                    -   以列表形式，展示该厨师所有`已上架`的个人套餐。
                    -   每个套餐卡片包含：套餐主图、名称、价格、适用人数、菜品清单摘要。
                    -   卡片上有一个`预订此套餐`的按钮，点击后进入与该厨师和该套餐绑定的预约流程。
                -   **Tab 2: 个人主页**:
                    -   包含厨师的代表作图集、个人履历、获得的证书荣誉、健康证等。
                    -   展示"证件齐全"、"平台担保"等信任标识。
                -   **Tab 3: 用户评价**:
                    -   展示真实的用户评价列表，包含评分、文字和图片。
                -   **Tab 4: 可约时间**:
                    -   以日历形式，直观展示厨师未来一段时间的`可预约`、`已约满`、`休息`状态。
        5.  **预订路径**:
            -   **路径A - 预约厨师基础服务**: 用户点击页面顶部的`预约厨师`按钮。
            -   **路径B - 预订厨师个人套餐**: 用户在"招牌套餐"Tab下，点击某个具体套餐的`预订此套餐`按钮。
        6.  **预约页面:**
            -   **路径A (预约基础服务):**
                -   页面顶部明确显示已选定的厨师信息和基础服务费。
                -   用户需选择服务地址和在厨师可预约的时间内选择`用餐时间`。
                -   提供步进器，允许用户在基础服务包含的菜品数之上，额外`增加加工菜品数量`，并实时更新费用。
                -   提供可选的增值服务模块。
            -   **路径B (预订个人套餐):**
                -   页面顶部明确显示已选定的厨师信息和**已选的套餐信息（名称、价格）**。
                -   用户只需选择服务地址和在厨师可预约的时间内选择`用餐时间`。
                -   套餐内容和价格固定，不可修改。
                -   提供可选的增值服务模块。
    -   **数据要素:**
        -   **输入:** 筛选条件、选定的厨师ID、(路径A: 增加的菜品数) 或 (路径B: 选定的套餐ID)、服务地址、服务时间、增值服务。
        -   **处理:** 锁定厨师档期，根据用户的选择路径和额外需求计算总费用。
        -   **输出:** 生成待支付的、与特定厨师绑定的订单。
    -   **业务规则:**
        -   厨师的服务费、擅长菜系、可服务时间等均由厨师在厨师端自主配置，经平台审核后生效。
        -   用户选择并预订成功后，该厨师对应的时间段将被锁定，其他用户无法再预约。
    -   **后续流程:** 用户确认信息后，进入通用支付流程 (FR4.x)。支付成功后，订单直接指派给该厨师。
-   **FR3.4 - "私人定制"模式 (双轨制) (USER-FLOW-004)**:
    -   **用户故事:**
        -   **(标准流程):** "公司要举办一场重要的庆祝晚宴，我希望能按人均预算，预订个性化的主题宴席服务，省去繁琐的沟通，并确保服务质量。"
        -   **(高端流程):** "我需要为一次非常重要的私人宴会寻找顶级的烹饪服务，例如国宴或米其林级别，我希望平台能提供一个专属的咨询渠道，让专家来对接我的复杂需求。"
    -   **规则:** 此模式分为两种路径，一种是标准化的产品流程，另一种是销售线索收集流程。
    -   **核心流程与界面元素 - 标准流程 (主题宴席):**
        1.  用户在"私人定制"分类下，选择一个明确的主题宴席和用餐标准（如"商务庆功宴"、"高端家宴"）。
        2.  **宴席详情页:**
            -   清晰地展示`按人均收费`的模式（如 ¥500/位）。
            -   展示宴席的参考菜单、服务标准、包含的服务员数量等。
            -   明确告知`超时服务费`和`法定节假日附加费`的计算规则。
        3.  用户点击"立即预订"，进入预约页面。
        4.  **预约页面:**
            -   用户核心操作是填写`用餐人数`。
            -   系统根据 `人均费用 × 人数` 动态计算基础总价。
            -   用户选择服务地址和时间。
            -   提供可选的增值服务模块。
            -   可以增加用餐人数
        5.  后续进入通用支付流程。
    -   **核心流程与界面元素 - 高端流程 (国宴/米其林):**
        1.  用户在"私人定制"分类下，点击"国宴主厨"或"米其林星厨"等高端入口。
        2.  进入一个销售线索收集页面，而非直接的预订流程。
        3.  **咨询表单页:**
            -   页面顶部会强调"此为高端定制咨询，提交后将有专属顾问与您联系"。
            -   **表单字段:** 宴会类型、预算范围、用餐人数、期望日期、联系人姓名、联系电话、特殊需求描述。
        4.  用户填写并提交表单。
        5.  **提交成功:**
            -   系统提示"您的需求已收到，专属顾问将会在24小时内与您取得联系"。
            -   此条需求作为销售线索，进入后台管理系统的"主题咨询"队列。
    -   **数据要素:**
        -   **(标准流程):** 选定的主题、人数、地址、时间、增值服务 -> 生成订单。
        -   **(高端流程):** 咨询表单内的所有信息 -> 生成销售线索。
    -   **业务规则:**
        -   标准流程的宴席主题、菜单、定价等由平台在后台配置。
        -   高端流程的入口仅用于收集信息，所有后续沟通、报价、签约均在线下由销售团队完成。
    -   **后续流程:**
        -   **(标准流程):** 进入通用支付流程 (FR4.x)。
        -   **(高端流程):** 流程转至线下，由后台CRM系统跟进。
-   **FR3.5 - "主题宴席"模式 (USER-FLOW-005)**:
    -   **用户故事:** "为庆祝宝宝的百日宴，我需要一个标准化的、按桌计算的宴席方案，希望能像在酒店订餐一样，直接选择桌数并完成预订，过程简单明了。"
    -   **规则**: 将宴席服务产品化，按"桌"为单位售卖，适用于公司团建、家庭大聚会等场景。
    -   **核心流程与界面元素:**
        1.  用户点击"主题宴席"入口，进入宴席列表页。
        2.  **宴席列表页:** 以卡片形式展示不同的宴席套餐，如"宝宝百日宴"、"企业年会餐"等。
        3.  **宴席详情页:**
            -   清晰展示宴席的`总价/桌`。
            -   详细列出该宴席一桌包含的所有菜品清单。
            -   明确注明`建议单桌人数`（如8-10人）。
            -   菜单需支持后台灵活配置，运营人员可以根据季节或活动更换菜品。
        4.  用户点击"立即预订"，进入预约信息填写页面。
        5.  **预约页面:**
            -   **核心操作:** 用户通过步进器选择`用餐桌数`。
            -   **总价计算:** 订单总价 = `单桌餐标 × 桌数` + `增值服务费`。
            -   用户需选择服务地址和用餐时间。
    -   **数据要素:**
        -   **输入:** 选定的宴席套餐ID、桌数、加位数、地址、时间、增值服务。
        -   **处理:** 根据定价规则计算最终订单总额。
        -   **输出:** 生成待支付的宴席订单。
    -   **业务规则:**
        -   宴席套餐为标准化产品，用户不可在前端修改菜品。
        -   厨师团队由平台根据宴席规格和区域进行统一指派。
    -   **后续流程:** 用户确认信息后，进入通用支付流程 (FR4.x)。
##### 3.1.4. 通用预约与支付流程 (Common Booking & Payment Flow)
-   **FR4.1 - 增值服务 (USER-FLOW-006)**:
    -   **用户故事:** "预订厨师上门服务时，我家里的餐具可能不够，而且我也不想在饭后洗碗。我希望在下单时能方便地选择租用餐具和需要餐后清洁的服务，让整个体验更轻松。"
    -   **规则**:
        -   增值服务作为独立模块，可被所有服务模式的预约流程复用。
        -   服务内容和价格由平台统一配置。
    -   **核心流程与界面元素:**
        1.  在用户填写核心预约信息（如时间、地址）的同一页面，下方会展示`增值服务`模块。
        2.  **服务列表:** 以列表或带图标的网格形式，清晰展示各项增值服务，如：
            -   `租用厨具` (按套计费)
            -   `租用餐具` (按套计费)
            -   `携带配料`
            -   `代买食材` (按服务费+食材费计费)
            -   `餐后清洁` (固定服务费)
            -   `指派服务员` (按小时或人头计费)
        3.  **交互方式:** 用户通过勾选框（Checkbox）或开关（Switch）来选择所需服务。
        4.  **数量调整:** 对于需要指定数量的服务（如餐具），旁边提供步进器（Stepper）供用户增减。
        5.  **费用联动:** 用户每选择一项或调整数量，订单总价实时更新，并在费用明细中单独列出`增值服务费`。
    -   **数据要素:**
        -   **输入:** 用户勾选的服务项ID、对应数量。
        -   **处理:** 根据平台设定的计价规则，计算各项增值服务的费用，并汇总计入订单总额。
        -   **输出:** 订单数据中包含增值服务的详细列表和总费用。
    -   **业务规则:**
        -   部分增值服务可能与特定服务模式或区域绑定（如"代买食材"不适用于"精选套餐"模式）。
        -   后台需支持对增值服务的灵活配置，包括名称、描述、图标、计费方式、价格等。
-   **FR4.2 - 地址管理 (USER-FLOW-007)**:
    -   **用户故事:** "我经常在家里和公司两个地方预订服务。我希望能预先保存好这两个地址，下单时直接选择，而不需要每次都重新手动输入，这样能节省很多时间。"
    -   **规则**:
        -   用户可以维护一个服务地址簿。
        -   地址信息与用户账户关联。
    -   **核心流程与界面元素:**
        1.  **地址选择:** 在预约流程中，地址输入框旁边提供一个`地址簿`图标或按钮。
        2.  **地址列表:** 点击后，弹出一个浮层或跳转到新页面，展示用户已保存的地址列表。
            -   每个地址项都清晰显示收货人姓名、手机号和详细地址。
            -   提供一个`默认地址`标签，用户可以设置最常用的地址。
            -   提供`编辑`和`删除`按钮。
        3.  **选择操作:** 用户点击列表中的任意地址，该地址信息会自动填充到预约页面的地址栏中。
        4.  **新增地址:** 列表底部提供一个`+ 新增服务地址`的按钮。
        5.  **新增/编辑页:**
            -   包含收货人、手机号、所在地区（省市区联动选择）、详细地址（门牌号）等输入框。
            -   提供`设为默认地址`的开关。
            -   保存后，新地址会出现在地址列表中。
    -   **数据要素:**
        -   **输入:** 用户输入的地址相关字段信息。
        -   **处理:** 对地址数据进行增、删、改、查操作，并支持设置默认值。
        -   **输出:** 用户地址簿列表，以及用户选定的单个地址信息。
    -   **业务规则:**
        -   首次下单的用户，在填写地址后，可提示用户`保存为常用地址`。
        -   地址合法性进行基础校验（如手机号格式）。
-   **FR4.3 - 优惠券与折扣 (USER-FLOW-008)**:
    -   **用户故事:** "我账户里有一张平台赠送的8折优惠券，我希望在这次支付前能方便地找到并使用它，这样可以省下一笔钱。同时，如果我是VIP会员，我希望能得到更多的优惠券。"
    -   **规则**:
        -   优惠券是主要的优惠方式。
    -   **核心流程与界面元素:**
        1.  **优惠入口:** 在订单确认页（支付前），会有一个`优惠`或`优惠券`的条目，默认显示`'x'张可用`或`未使用`。
        2.  **优惠选择页:** 点击后，进入优惠选择页面。
            -   **优惠券列表:** 顶部展示所有`可用`的优惠券。每张券清晰标注折扣额度/金额、使用门槛、适用范围和有效期。
            -   **不可用列表:** 提供一个折叠或跳转链接，展示`不可用`的优惠券及其原因（如未到使用门槛、不适用当前服务）。
            -   用户通过单选框（Radio Button）在不同优惠券和VIP折扣之间进行选择。
            -   提供一个`不使用优惠`的选项。
        3.  **应用优惠:** 用户选择一项优惠后返回订单确认页，`优惠`条目会更新为所选的优惠名称和抵扣的金额，订单总价也随之刷新。
    -   **数据要素:**
        -   **输入:** 用户选择的优惠券ID。
        -   **处理:** 系统校验优惠的可用性，计算抵扣金额，更新订单总价。
        -   **输出:** 订单数据中记录使用的优惠信息和最终支付金额。
    -   **业务规则:**
        -   后台需支持创建多种类型的优惠券：满减券、折扣券、代金券。
-   **FR4.4 - 支付与订单确认 (USER-FLOW-009)**:
    -   **用户故事**: "我已经确认了所有订单信息和费用，现在我希望能快速、安全地完成支付。如果我暂时不方便，也希望能让朋友帮我支付。"
    -   **规则**:
        -   支付是订单创建的最后一步。
        -   支付成功后，订单状态变为`待接单`。
    -   **核心流程与界面元素:**
        1.  **订单确认页:** 这是支付前的最后一步，页面上会汇总展示所有信息：
            -   服务模式、预约时间、服务地址。
            -   **费用明细:** 清晰列出`基础服务费`、`增值服务费`、`优惠抵扣`和最终的`合计`金额。
            -   **支付方式:** 默认选择`微信支付`。
        2.  **主操作按钮:** 页面底部有一个醒目的`立即支付`按钮。
        3.  **微信支付:** 点击后，拉起微信支付组件，用户输入密码或指纹完成支付。
        4.  **找人代付 (可选功能):**
            -   `立即支付`按钮旁，提供一个`找人代付`的次要按钮或入口。
            -   点击后，生成一个代付分享卡片，用户可以将其发送给微信好友。
            -   卡片上需包含订单的简要信息（如"XX的美食服务"）和需支付的金额。
            -   好友点击卡片即可为该订单完成支付。
        5.  **支付结果页:**
            -   **支付成功:** 显示成功状态，并告知用户`平台正在为您安排厨师`，提供`查看订单`和`返回首页`的按钮。
            -   **支付失败:** 显示失败状态，并提供`重新支付`的选项。
    -   **数据要素:**
        -   **输入:** 用户的支付指令或代付人的支付动作。
        -   **处理:** 与微信支付网关交互，处理支付请求，更新订单状态。
        -   **输出:** 支付成功或失败的最终状态，并同步更新后台订单数据。
    -   **业务规则:**
        -   订单在`待支付`状态下有时效性（如30分钟），超时未支付则自动取消。
        -   代付链接同样具有时效性。
-   **FR4.5 - 协议与授权 (USER-FLOW-010)**:
    -   **用户故事**: "在付款前，我需要确保我同意了平台的服务条款。付款后，我希望能及时收到订单状态的更新通知，而不是总要自己打开App查看。"
    -   **规则**:
        -   用户协议同意是支付的前置条件。
        -   订阅消息是提升用户体验的关键环节。
    -   **核心流程与界面元素:**
        1.  **协议同意:**
            -   在订单确认页的`立即支付`按钮上方，必须有一个默认未勾选的复选框（Checkbox）。
            -   复选框旁边的文案为："我已阅读并同意《用户服务协议》和《隐私政策》"。
            -   协议名称为可点击链接，点击后会弹出展示协议全文的浮层或页面。
            -   只有当用户勾选此框后，`立即支付`按钮才变为可点击状态。
        2.  **订阅消息授权:**
            -   在**支付成功后**的跳转页面上，会弹出微信官方的订阅消息授权窗口。
            -   窗口会提示用户，授权后即可接收`订单状态通知`、`服务提醒`等。
            -   用户可选择`允许`或`取消`。即使取消，也不影响订单流程。
    -   **数据要素:**
        -   **输入:** 用户勾选协议的动作、用户对订阅消息的授权结果。
        -   **处理:** 前端记录协议勾选状态以激活支付按钮；后端记录用户的订阅消息权限，用于后续推送。
        -   **输出:** N/A
    -   **业务规则:**
        -   协议内容需由后台统一管理，方便更新。
        -   需规划好要申请的订阅消息模板，如`订单支付成功通知`、`厨师已接单通知`、`服务即将开始提醒`等。
-   **FR4.6 - 退款政策 (USER-FLOW-011)**:
    -   **用户故事**: "我预订了一周后的服务，但计划可能有变。我希望在付款前就清楚地知道，如果我需要取消订单，能在什么时间点前获得全额退款，什么时间点会扣除费用，避免事后产生纠纷。"
    -   **规则**:
        -   退款政策的透明化是建立用户信任的基础。
        -   政策展示应在用户付款前完成。
    -   **核心流程与界面元素:**
        1.  **信息展示点:** 在订单确认页的费用总计下方，或`立即支付`按钮附近，提供一个清晰的、可点击的`退款政策`链接。
        2.  **政策详情:** 点击链接后，弹出一个浮层或页面，用清晰、易于理解的语言（而非法律条文），以时间轴或列表形式展示阶梯式退款规则。例如：
            -   **服务开始前72小时:** 可获100%全额退款。
            -   **服务开始前24-72小时:** 退还订单金额的50%。
            -   **服务开始前24小时内:** 不予退款。
            -   *(具体时间节点和比例由运营策略决定)*
        3.  **用户确认:** 无需用户强制勾选，但其存在即代表平台已尽到告知义务。
    -   **数据要素:** N/A (此为静态信息展示)
    -   **业务规则:**
        -   退款政策的具体规则（时间节点、退款比例）需支持后台灵活配置。
        -   规则文案需清晰明了，避免歧义。
##### 3.1.5. 订单中心 (Order Center)
-   **FR5.1 - 订单列表 (USER-FLOW-012)**:
    -   **用户故事:** "我想看看我所有的订单，特别是快要到来的服务，希望能快速找到它。我也想方便地找到已经完成但还没评价的订单，好给厨师一个反馈。"
    -   **规则**: 订单列表是用户管理所有交易的核心入口。
    -   **核心流程与界面元素:**
        1.  **Tab式导航:** 页面顶部采用Tab栏进行订单状态分类，是此页面的核心交互。至少包含：
            -   `全部`
            -   `待付款`
            -   `待服务` (包含待接单、待上门)
            -   `待评价`
            -   `退款/售后`
        2.  **订单卡片:** 在Tab切换后的列表中，每个订单以卡片形式展示。卡片上需包含关键信息：
            -   `订单状态` (如：待接单、服务中)
            -   `服务名称` 或 `厨师姓名`
            -   `服务时间`
            -   `实付金额`
            -   `操作按钮` (如：去支付、去评价、申请售后、再次预订)
        3.  **空状态:** 每个Tab下如果没有任何订单，需要有清晰的空状态提示，如"您还没有待评价的订单哦"，并提供一个`去逛逛`的按钮引导用户去首页。
        4.  **特殊咨询单:** 需包含一个特殊的"主题咨询"分类，用于管理来自"私人定制(高端)"的销售线索，此列表仅用户自己可见。
    -   **数据要素:**
        -   **输入:** 用户点击的Tab状态。
        -   **处理:** 根据所选状态，从后端筛选并拉取对应的订单数据。
        -   **输出:** 分好类的订单列表。
    -   **业务规则:**
        -   订单状态的合并与分类需清晰定义。例如`待服务`可以聚合后台的`待接单`和`待上门`两种状态。
        -   卡片上展示的操作按钮需根据订单状态动态变化。
-   **FR5.2 - 订单详情 (USER-FLOW-013)**:
    -   **用户故事**: "我想查看某个订单的全部细节，比如我当时选了哪些增值服务，以及厨师的具体信息。如果服务快开始了，我希望能直接联系到厨师。"
    -   **规则**: 订单详情页是订单信息的最终载体，其内容和可操作项必须随订单状态实时变化。
    -   **核心流程与界面元素:**
        1.  **页面结构:**
            -   **顶部:** 醒目地展示当前`订单状态`（如：待服务）和状态描述（如：厨师将于xx时间上门）。
            -   **中部:**
                -   服务信息（套餐/厨师名称、服务时间、地址）。
                -   厨师信息卡片（当厨师已分配时显示），包含头像、姓名，并提供`联系厨师`的按钮。
                -   账单明细（服务费、增值服务费、优惠、实付款）。
            -   **底部:**
                -   订单信息（订单号、下单时间、支付方式）。
                -   `操作按钮区`。
        2.  **动态内容与操作 (核心):**
            -   **待付款:** 不显示厨师信息。操作按钮为`立即支付`、`取消订单`。
            -   **待接单:** 不显示厨师信息。操作按钮为`取消订单`、`提醒平台派单`。
            -   **待服务 (已接单):** **必须显示厨师信息和联系按钮**。操作按钮为`申请售后`。
            -   **服务中:** 状态更新为"服务中"。操作按钮为`申请售后`。
            -   **待评价:** 状态更新为"服务已完成"。操作按钮为`去评价`、`再次预订`。
            -   **已完成/已评价:** 操作按钮为`再次预订`、`删除订单`。
            -   **退款中/已退款:** 展示退款进度和详情。
    -   **数据要素:**
        -   **输入:** 订单ID。
        -   **处理:** 后端根据订单ID和当前状态，聚合所有相关信息（订单、厨师、商品、账单）并返回给前端。
        -   **输出:** 完整的、符合当前状态的订单详情数据。
    -   **业务规则:**
        -   `联系厨师`功能可提供厨师电话。或由平台客服转联系，保护双方隐私。
        -   `取消订单`操作需遵循`FR4.6 - 退款政策`的规则。
-   **FR5.3 - 订单搜索**:
    -   **用户故事**: "我记得上个月订过一个川菜厨师，名字忘了，想找出来再订一次。我希望能通过服务类型、厨师名、套餐名等关键词，快速从我的订单历史中找到那笔订单。"
    -   **核心流程与界面元素**:
        1.  **搜索入口**: 在订单列表页的顶部，提供一个常驻的搜索框。
        2.  **搜索交互**: 用户在搜索框内输入关键词，下方的订单列表会实时根据关键词进行筛选，展示匹配的结果。
        3.  **搜索结果**: 搜索结果直接在当前的Tab分类下展示（如在"全部"Tab下搜索，则在所有历史订单中匹配）。
        4.  **清空按钮**: 搜索框内提供一键清空的功能，清空后恢复列表的原始状态。
##### 3.1.6. 个人中心 (My Profile)
-   **FR6.1 - 核心功能入口**:
    -   **用户故事**: "作为用户，我希望'我的'页面能清晰地展示我的头像和昵称，并能让我快速找到所有与我账户相关的功能，如订单、优惠券和地址管理。"
    -   **核心流程与界面元素**:
        1.  **顶部个人信息区**:
            -   展示用户的微信头像和昵称。
            -   如果用户是VIP会员，需有明显的VIP标识或入口。
        2.  **核心功能矩阵**:
            -   以网格布局（Grid）或列表（List）形式，清晰地展示各项功能的入口。
            -   **必备入口**: `我的订单`、`我的评价`、`地址管理`、`优惠券`。
            -   **运营入口**: `联系客服`、`帮助中心`、`关于我们`。
            -   **营销入口**: `成为推广员`（对所有用户可见，点击后进入申请流程）。
    -   **数据要素**:
        -   **输入**: 用户ID。
        -   **处理**: 后端返回该用户的基本信息（昵称、头像）和身份状态（是否为VIP、是否为推广员）。
        -   **输出**: 用于渲染个人中心页面的数据。
    -   **业务规则**:
        -   `成为推广员`入口在用户成功申请后，应自动变为`推广员中心`。
-   **FR6.2 - VIP会员**:
    -   **用户故事**: "作为平台的常客，我希望能通过开通VIP会员享受到更多专属福利，比如每月固定的优惠券，优先指派vip厨师。这会让我觉得自己的消费是值得的。"
    -   **核心流程与界面元素**:
        1.  **VIP中心页**: 从个人中心的入口进入。
            -   页面顶部展示用户当前的VIP状态（非会员/VIP会员及到期日）。
            -   **权益列表**: 清晰、吸引人地展示VIP会员的核心权益，如：
                -   `专享优惠券` 。
                -   `vip明星厨师优先指派`。
        2.  **支付流程**: 复用平台的通用支付模块，完成VIP购买。
    -   **数据要素**:
        -   **输入**: 用户选择的VIP套餐。
        -   **处理**: 创建或延长用户的VIP会籍，记录起止时间。
        -   **输出**: 更新后的用户VIP状态。
    -   **业务规则**:
        -   VIP权益（如折扣）需要在下单时自动计算和应用。
        -   所有VIP相关的配置（价格、权益描述）需支持后台灵活管理。
#### 3.2. 厨师端小程序 (Chef Mini Program)
##### 3.2.1. 厨师入驻与认证 (Onboarding & Verification)
-   **FR7.1 - 开放注册**:
    -   **用户故事**: "作为一名有专业技能的厨师，我希望平台能提供一个简单明了的入口，让我能快速发起入驻申请，开启我的线上接单生涯。"
    -   **核心流程与界面元素**:
        1.  **入驻入口**: 在用户端小程序的个人中心或通过专门的推广二维码，提供一个清晰的`厨师入驻`按钮。
        2.  **初始申请页**: 点击后，进入一个极简的申请页面。
            -   **核心字段**: `手机号码`、`短信验证码`。
            -   需勾选同意《厨师平台服务协议》。
        3.  **提交与跳转**: 提交成功后，系统在后端创建一个状态为`待完善资料`的厨师账户。
        4.  **引导下载/打开**: 页面提示用户"申请已收到，请前往厨师端小程序完善资料以完成入驻"，并提供一键跳转链接。
    -   **数据要素**:
        -   **输入**: 手机号、验证码。
        -   **处理**: 后端验证手机号，创建初始厨师账户。
        -   **输出**: 一个新的、未激活的厨师账户。
    -   **业务规则**:
        -   一个手机号可以同时注册为用户和厨师。如果是厨师，默认进入厨师端

-   **FR7.2 - 渐进式资料完善**:
    -   **用户故事**: "当我第一次登录厨师端时，我希望能有一个清晰的引导，告诉我需要完成哪些步骤才能开始接单，而不是面对一个复杂的、充满空白输入框的页面而不知所措。我希望能像游戏里的任务列表一样，一步步完成它们。"
    -   **规则**: 新厨师首次登录时，必须展示一个引导式的入驻流程，将复杂的资料填写任务分解。
    -   **核心流程与界面元素**:
        1.  **完善信息**: 厨师首次使用手机号登录厨师端小程序，可以显示主界面，但功能需要完善信息后可以使用。
    -   **业务规则**:
        -   在所有必填项完成并提交审核前，厨师可以访问订单、财务等核心功能，但不显示内容。
        -   如果审核被驳回，高亮显示被驳回的板块及驳回原因，引导厨师修改。

-   **FR7.3 - 个人资料管理**:
    -   **用户故事**: "作为一名厨师，我希望有一个全面且易于使用的后台，可以上传专业的形象照，详细介绍我的烹饪特长和经验，设定我的服务价格，并展示我最好的菜品照片集。这将帮助我在平台上建立个人品牌，吸引更多客户。"
    -   **规则**: 为厨师提供一个功能强大的、多Tab页的界面，以管理其所有面向公众的个人资料和服务能力。
    -   **核心流程与界面元素**: 个人资料管理将采用Tab页结构，以保证清晰性。
        1.  **Tab 1: 基本信息 (Basic Information)**
            -   `专业形象照 (Professional Photo)`: 上传组件，附有高质量头像的拍摄指南。
            -   `厨师昵称 (Chef Nickname)`: 文本输入框。
            -   `个人格言/一句话介绍 (Tagline/Motto)`: 文本输入框，有字数限制。
        2.  **Tab 2: 职业履历 (Professional Experience)**
            -   `厨龄 (Years of Experience)`: 数字输入框。
            -   `工作履历 (Work History)`: 一个可重复的组件，用于添加过往的工作经历（餐厅、职位、任职时间）。
            -   `健康证 (Health Certificate)`: 图片上传组件，并带有一个`到期日 (Expiry Date)`选择器。
            -   `资质证书 (Qualifications)`: 图片上传组件，允许上传多个证书。
            -   `所获荣誉 (Honors)`: 用于描述的文本区域。
        3.  **Tab 3: 服务信息 (Service Information)**
            -   `擅长菜系 (Specialized Cuisines)`: 多选复选框（如川菜、粤菜等，由后台配置）。
            -   `基础服务费 (Base Service Fee)`: 数字输入框，并有辅助文字说明其覆盖范围（例如，"单点厨师模式下，含8菜1汤的服务费"）。
            -   `增值服务 (Value-Added Services)`: 一个由平台定义的服务列表（如餐后清洁、自带厨具），厨师可通过复选框选择加入。
            -   `我的菜品 (My Dishes Portfolio)`: 一个图库管理组件，允许厨师上传多张高质量的菜品图片，并为每张图片添加可选的名称和描述。
        4.  **保存/提交**: 每个Tab页都有一个"保存"按钮。只有在所有Tab页的必填字段都填写完毕后，主"提交审核"按钮才会变为激活状态。
    -   **数据要素**:
        -   **输入**: 文本字段、数字输入、日期选择器、图片上传。
        -   **处理**: 后端保存数据，并将其与厨师账户关联。图片存储在云存储桶中。资料变更会触发"待审核"状态。
        -   **输出**: 一个结构化且内容丰富的厨师个人资料，可供用户端和后台管理系统使用。
    -   **业务规则**:
        -   系统必须在厨师的健康证到期前30天，向其发送通知（如短信、应用内消息）。
        -   所有用户上传的文本和图片均需接受平台管理员的内容审核。任何对关键信息（如服务费、姓名）的修改都必须重新获得批准。
-   **FR7.4 - 实名认证 (强制)**:
    -   **用户故事**: "作为一个平台，为了确保用户的安全并建立信任，我们需要在每位厨师开始接单前验证其真实身份。作为一名厨师，我希望这个过程安全、直接，最好是使用权威的第三方系统来完成。"
    -   **规则**: 实名认证是强制性的一次性流程，必须在厨师的个人资料被激活之前完成。
    -   **核心流程与界面元素**:
        1.  此步骤将作为渐进式入驻流程中的最后一个关键任务呈现。
        2.  界面将明确说明："为保障用户安全，您需要完成实名认证后方可接单。"
        3.  显示一个单独的按钮，`前往认证`。
        4.  点击后，小程序将调用并跳转至一个经过认证的第三方认证小程序（如腾讯云E-KYC或像eID这样的政府服务）。
        5.  厨师在第三方服务中完成人脸识别和身份证号码验证流程。
        6.  完成后，第三方服务将用户重定向回厨师端小程序，并传递一个安全令牌或结果。
        7.  后端验证此结果，并更新厨师的认证状态为`已认证`。
        8.  厨师端的用户界面上会更新显示一个"已认证"的徽章。
    -   **数据要素**:
        -   **输入**: N/A (用户交互在第三方服务中进行)。
        -   **处理**: 后端从第三方服务接收认证结果（通过/失败）。
        -   **输出**: 厨师个人资料中的一个已认证标志 (`is_verified: true`)。
    -   **业务规则**:
        -   没有`已认证`的实名状态，厨师的个人资料无法提交最终审核或被激活。
        -   平台不直接存储身份证号码等敏感用户数据，仅存储验证结果和来自第三方服务的唯一标识符。
        -   如果验证失败，应允许用户在限定次数内重试。
##### 3.2.2. 服务与套餐管理 (Service & Package Management)
-   **FR8.1 - 个人套餐创建**:
    -   **用户故事**: "我希望能创建一份招牌套餐，比如'正宗川味四人宴'，设定好固定的价格和清晰的菜品列表，这样用户就能方便地预订我的特色服务，而不需要过多沟通。"
    -   **规则**: 厨师可以创建多个、各具特色的个人套餐，每个套餐都是一个独立的可预订项。
    -   **核心流程与界面元素**:
        1.  厨师通过导航进入"我的服务" -> "套餐管理"，点击`+ 创建新套餐`按钮。
        2.  进入一个用于创建套餐的表单页面，包含以下字段：
            -   `套餐主图`: 图片上传组件。
            -   `套餐名称`: 文本输入，如"高端海鲜家宴"。
            -   `套餐价格`: 数字输入。
            -   `适用人数`: 范围选择器或两个数字输入框（如，从4到6人）。
            -   `菜品数量`: 文本输入，如"8菜1汤"。
        3.  一个用于`菜品清单`的动态管理区域：
            -   提供`+ 添加类别`按钮（如凉菜、热菜、汤羹）。
            -   在每个类别下，有`+ 添加菜品`按钮，点击后增加一个用于填写菜品名称的文本框。
            -   支持对类别和菜品进行拖拽排序和删除。
        4.  页面底部提供`存为草稿`和`提交审核`按钮。
    -   **数据要素**:
        -   **输入**: 表单中的所有字段（文本、数字、图片、结构化的菜品列表）。
        -   **处理**: 后端保存套餐数据，将其与厨师关联，并设置状态为`待审核`。
        -   **输出**: 数据库中生成一个新的套餐对象。
    -   **业务规则**:
        -   所有新增或修改的套餐，都必须经过平台管理员审核批准后，才能在用户端展示。
        -   厨师必须处于`已认证`状态才能创建套餐。
-   **FR8.2 - 套餐管理**:
    -   **用户故事**: "我想能方便地查看我创建的所有套餐，有时候需要编辑一下价格，或者把一个季节性套餐暂时下架，也可以把过时的套餐永久删除。"
    -   **核心流程与界面元素**:
        1.  一个列表页面，展示厨师创建的所有个人套餐。
        2.  每个套餐以卡片形式展示，包含名称、主图、价格和当前状态（`审核中`、`已上架`、`已下架`）。
        3.  每张卡片上都有明确的操作按钮：`编辑`、`下架/上架`、`删除`。
        4.  `编辑`操作将用户带到与`FR8.1`相同的表单页面，并预先填好该套餐的数据。
        5.  `下架/上架`操作可快速切换套餐的可见状态。
        6.  `删除`操作会弹出二次确认提示。
    -   **业务规则**:
        -   `下架`操作会使用户端看不到该套餐，但数据会为厨师保留。
        -   编辑一个`已上架`的套餐，会将其状态暂时变为`待审核`，在重新批准前，用户端将看不到该套餐。
        -   如果一个套餐关联了任何未完成的订单，则不允许被`删除`。
-   **FR8.3 - 平台审核**:
    -   **规则**: 所有由厨师创建或修改的套餐，都必须提交至后台管理系统。管理员需要对提交的内容进行审核，并可选择`批准`或`驳回`（需填写理由）。只有状态为`已批准`的套餐才能在用户端小程序中展示。此需求关联后台管理系统`FR13.1`。
##### 3.2.3. 订单管理 (Order Management)
-   **FR9.1 - 工作台 (处理订单)**:
    -   **用户故事**: "作为一名忙碌的厨师，我需要一个清晰、简洁的工作台，能让我一眼看到所有新订单、待处理订单和进行中的订单，这样我才能高效地安排我的日程，不错过任何重要任务。"
    -   **规则**: 工作台是订单管理模块的默认视图，必须优先展示需要厨师立即处理的信息。
    -   **核心流程与界面元素**:
        1.  一个仪表盘式的界面。
        2.  **核心指标展示**: 在页面顶部醒目地显示`待接单`、`待服务`、`服务中`状态的订单计数。
        3.  **订单列表**: 在计数器下方，使用列表或卡片清晰地展示上述状态的订单。
        4.  **订单卡片**: 列表中的每张卡片需简洁明了，包含：
            -   `服务时间` (如: "明天 18:00")
            -   `服务地址` (如: "XX小区，XX栋")
            -   `服务类型` (如: "六菜一汤" 或 "精选套餐")
            -   `订单收入`
            -   一个主要操作按钮（如：`接单`、`查看详情`）。
        5.  **搜索栏**: 页面顶部提供一个搜索栏，支持按顾客姓名或手机号快速查找当前任务。
    -   **数据要素**:
        -   **输入**: 厨师ID。
        -   **处理**: 后端查询与该厨师关联的、所有状态为`待接单`、`待服务`、`服务中`的订单。
        -   **输出**: 结构化的、待处理的订单列表。
    -   **业务规则**:
        -   由平台指派的`待接单`订单，会有一个接单倒计时。若超时未接，订单可能会被系统改派给其他厨师。
-   **FR9.2 - 订单历史 (所有订单)**:
    -   **用户故事**: "我希望能回顾我完成过的所有工作，查一下某笔订单的具体收入，或者看看某个客户是否给我留了评价。"
    -   **核心流程与界面元素**:
        1.  与"工作台"分开的独立页面或Tab。
        2.  顶部提供Tab式导航栏，用于按历史状态筛选：`全部`、`已完成`、`已评价`、`已取消`。
        3.  列表中的订单卡片展示与历史记录相关的信息，例如在已评价的订单上显示最终评分。
        4.  支持按服务完成的`月份`进行筛选或搜索。
    -   **数据要素**:
        -   **输入**: 厨师ID、选择的状态过滤器、可选的日期范围。
        -   **处理**: 后端根据筛选条件，拉取并分页展示厨师的所有历史订单。
        -   **输出**: 历史订单列表。
-   **FR9.3 - 订单操作**:
    -   **用户故事**: "当我收到新订单时，我需要快速决定是接单还是拒绝。对于即将开始的工作，我需要能联系上客户确认细节。服务结束后，我得把订单标记为完成，这样才能收到钱。"
    -   **核心流程与规则**:
        1.  **接单/拒绝 (Accept/Decline)**:
            -   对于`待接单`的订单，提供两个明确的按钮。
            -   点击`接单`，订单状态变更为`待服务`，系统向用户发送通知，同时该厨师对应的时间段被完全锁定。
            -   点击`拒绝`，厨师需要从一个预设的理由列表（如"时间冲突"、"距离太远"）中选择一项，随后订单将返回平台的派单池进行重新分配。
        2.  **联系客户 (Contact Customer)**:
            -   在`待服务`和`服务中`状态的订单详情中提供此按钮。
            -   该功能将通过客户的号码。或由平台客服来发起，以保护双方的隐私。
        3.  **确认完成 (Confirm Completion)**:
            -   这是厨师在订单上的最后一个操作，在订单状态为`服务中`时可用。
            -   点击后，系统会弹出确认提示（"请确认您已完成所有服务内容"）。
            -   确认后，订单状态变更为`待评价`，此动作将触发平台的结算流程，并提示用户对本次服务进行评价。
##### 3.2.4. 个人中心与财务 (Profile & Finance)
-   **FR10.1 - 接单状态切换**:
    -   **用户故事**: "我有时会因为太忙或在休假而无法接单。我需要一个快速、明显的方式将我的状态设置为'休息中'，这样我就不会收到新的订单请求，当我准备好工作时也能轻松地切换回来。"
    -   **核心流程与界面元素**: 在"我的"主页顶部提供一个醒目的全局开关（Toggle Switch），允许厨师在`接单中`和`休息中`两个状态之间实时切换。
    -   **业务规则**: 当厨师状态为`休息中`时，他会出现在"单点厨师"的用户搜索结果中，但会排名靠后，也不会被纳入平台派单的候选池。
-   **FR10.2 - 服务时间管理**:
    -   **用户故事**: "除了简单的开关，我需要更精细的控制。我希望有一个日历，可以在上面标出我未来特定日期或时间段的空闲与否，这样我就只会收到符合我日程的预订。"
    -   **核心流程与界面元素**: 一个全屏的日历视图。厨师可以点击具体日期来切换当天的可预约状态（如`可预约` vs `休息`）。
    -   **业务规则**: 此处设置的可用性数据，将直接同步并控制用户在"单点厨师"流程中看到的该厨师的可预约日历。
-   **FR10.3 - 服务区域管理**:
    -   **用户故事**: "我下个月要搬到新的区了。我希望能申请更改我的主要服务区域，以便能接到我新位置周边的订单。"
    -   **核心流程与界面元素**: 一个简单的申请表单，厨师可以在其中从预设的区域列表（如城市的不同区）中选择一个新的目标服务区域，并提交申请。界面需清晰展示当前已批准的区域，以及任何待处理申请的状态（`审核中`、`已批准`）。
    -   **业务规则**: 服务区域的变更必须经过平台管理员批准，以确保服务网络的覆盖和质量。
-   **FR10.4 - 评价管理**:
    -   **用户故事**: "我希望能在一个地方看到所有顾客给我的反馈，这样我才能知道自己哪里做得好，哪里可以改进。"
    -   **核心流程与界面元素**: 一个评价列表页面，展示所有收到的用户评价。每条评价以卡片形式显示，包含星级评分、顾客评论以及关联的订单信息。
    -   **业务规则**: 在当前版本中，厨师只能查看评价，不能删除或回复。
-   **FR10.5 - 财务管理**:
    -   **用户故事**: "我需要一个清晰、可信的财务系统，让我能看到自己赚了多少钱，有多少可以提现，并能查到详细的收支和提现记录。当我想要提现时，过程必须简单、安全。"
    -   **核心流程与界面元素**:
        -   **收益总览**: 在个人中心页面，醒目地展示`总收入`和`可提现金额`。
        -   **提现流程**:
            1.  一个`提现`按钮，点击后进入提现流程。
            2.  首次使用的用户，系统会引导其绑定一个银行账户（需填写真实姓名、银行名称、银行卡号）。该信息将被安全保存以备后续使用。
            3.  在提现页面，用户输入希望提取的金额。页面需清晰展示平台的`服务费/佣金`比例，以及扣除后厨师最终将收到的`实际到账金额`。
            4.  用户提交提现申请。
        -   **账单中心**:
            1.  一个独立的账单中心页面，包含`收支明细`和`提现记录`两个Tab。
            2.  `收支明细`以时间倒序列表展示所有交易（如，"+ ¥599 来自订单 #12345"）。
            3.  `提现记录`展示所有提现申请及其处理状态（`处理中`、`已到账`、`失败`）。
    -   **业务规则**:
        -   订单收入遵循结算周期（如T+1），即服务完成后需经过一定时间才能转为`可提现金额`。
        -   平台会设定`最低提现金额`。
        -   所有提现申请都需经过平台财务部门的审核。
-   **FR10.6 - 营销活动**:
    -   **用户故事**: "我希望能参加平台的推广活动，比如'成为活动厨师'，参与新用户体验，以获得更多的曝光和订单。"
    -   **核心流程与界面元素**: 一个简单的活动页面，展示平台活动。活动上有`立即报名`的按钮。
    -   **业务规则**: 参与活动可能需要满足一定的前提条件（如最低好评率、擅长特定菜系等）。
#### 3.3. 后台管理系统 (Admin System)
##### 3.3.1. 厨师管理模块 (Chef Management)
-   **FR11.1 - 厨师列表与查询**:
    -   **用户故事**: "作为一名运营经理，我需要一个功能全面的厨师列表，可以按状态（待审核、已激活等）、位置、技能等多个维度进行筛选和搜索，以便快速找到我需要管理的厨师群体。"
    -   **核心流程与界面元素**:
        -   一个主数据网格（Data Grid），展示所有厨师的核心信息。
        -   **列**: 厨师ID、姓名、手机号、所在城市/区域、主营菜系、厨师等级、总订单数、好评率、当前状态（如`待审核`、`已激活`、`已暂停`）、注册日期。
        -   **筛选与搜索控件**:
            -   提供按厨师姓名、手机号的文本搜索框。
            -   提供按状态、区域、菜系、等级的下拉筛选器。
        -   每行提供一个`查看详情`的操作按钮。
-   **FR11.2 - 厨师入驻审核**:
    -   **用户故事**: "作为一名审核专员，我需要一个专门的工作队列，清晰地列出所有等待审核的新厨师申请。我必须能方便地查看他们提交的所有资料，并一次性做出批准或拒绝的决定。"
    -   **核心流程与界面元素**:
        1.  一个专门的审核页面或队列，仅显示状态为`待审核`的厨师。
        2.  点击一个申请后，进入审核详情页。此页面会并排或分块展示厨师提交的所有信息：基本信息、职业履历、服务信息、菜品图集。
        3.  特别高亮展示`健康证`和`资质证书`的图片，并显示第三方服务返回的`实名认证`结果（通过/失败）。
        4.  审核员在页面底部有两个主要操作按钮：`审核通过`和`审核不通过`。
        5.  若选择`审核不通过`，系统会要求审核员必须填写或选择一个驳回理由，该理由将通过系统消息发送给厨师。
    -   **业务规则**: 身份认证（实名认证）是审核通过的强制性前置条件。
-   **FR11.3 - 厨师详情与编辑**:
    -   **用户故事**: "我需要能随时进入任何一位厨师的详细档案，不仅为了查看他们的完整信息，也为了在必要时能进行管理操作，比如编辑他们的资料，或者在出现违规时暂停他们的账户。"
    -   **核心流程与界面元素**:
        1.  从厨师列表页进入，此页面以只读形式展示厨师的全部资料（与厨师端看到的一致）。
        2.  在页面顶部或侧边栏，提供关键的管理操作按钮：
            -   `编辑资料`: 将页面变为可编辑状态。
            -   `暂停账户/激活账户`: 切换厨师的接单资格。
            -   `调整等级`: 运营人员可手动调整厨师的平台等级。
            -   `查看订单历史`: 跳转到该厨师的所有订单列表。
    -   **业务规则**: 所有由后台管理员发起的关键信息修改（如调整服务费、等级）都需要在系统中留下操作日志，以备审计。
-   **FR11.4 - 区域更换申请管理**:
    -   **用户故事**: "我需要管理厨师们提出的服务区域变更请求。我得能看到他们想从哪个区换到哪个区，并根据公司的运营策略来决定是否批准。"
    -   **核心流程与界面元素**:
        1.  一个专门的申请队列页面，列出所有`待处理`的区域更换申请。
        2.  每条申请清晰地展示厨师姓名、当前区域、申请的新区域以及申请时间。
        3.  每行都有`批准`和`驳回`两个按钮。
        4.  操作后，申请状态更新，并通知厨师结果。
    -   **业务规则**: 批准后，系统需自动更新该厨师的个人资料和服务区域设置。
##### 3.3.2. 订单管理模块 (Order Management)
-   **FR12.1 - 订单列表与查询**:
    -   **用户故事**: "作为一名客服主管，我需要一个全局的订单视图，可以快速响应客户的问询，监控订单状态，并识别潜在问题。我需要强大的筛选工具来精确地找到我需要的订单信息。"
    -   **核心流程与界面元素**:
        -   一个主数据网格，展示所有历史和当前的订单。
        -   **列**: 订单ID、服务类型、服务时间、客户（姓名/电话）、厨师（姓名/电话）、订单状态、订单总金额。
        -   **全面的筛选控件**:
            -   按订单ID、客户信息、厨师信息进行文本搜索。
            -   按`订单状态`（待支付、待接单、待服务、服务中、待评价、已完成、已取消、退款中）进行下拉筛选。
            -   按`服务模式`（一口价、精选套餐等）进行下拉筛选。
            -   按`城市/区域`进行下拉筛选。
            -   按`服务时间`进行日期范围选择。
        -   每行提供一个`查看详情`的操作按钮。
    -   **业务规则**: 访问权限根据角色定义。例如，客服主管能看到所有订单，而调度员可能只关注`待接单`和`服务中`的订单。
-   **FR12.2 - 订单详情**:
    -   **用户故事**: "当客户来电咨询时，我需要在一个地方看到他们订单的全部细节——厨师是谁，订了什么，付了多少钱，以及完整的事件时间线——这样我才能提供准确快速的支持。"
    -   **核心流程与界面元素**:
        -   一个详细的订单视图页面，聚合了所有相关信息。
        -   **客户详情**: 姓名、联系电话、服务地址。
        -   **厨师详情**: 姓名、联系电话。
        -   **服务详情**: 服务模式、套餐/菜品详情、用户备注、增值服务列表。
        -   **财务详情**: 费用明细（基础服务费、增值服务费、优惠折扣、最终支付金额、支付状态）。
        -   **订单时间轴/日志**: 清晰地展示订单状态的每一次变化（如 创建 -> 支付 -> 指派 -> 服务开始 -> 完成 -> 评价）。
    -   **业务规则**: 敏感操作（如触发退款）应仅限于特定角色（如售后主管、财务）可见和操作。
-   **FR12.3 - 手动派单/改派**:
    -   **用户故事**: "有时自动派单会失败，或者厨师临时有事无法服务。作为一名调度员，我需要能手动为未分配的订单指派一位合适的厨师，或者为已有问题的订单重新指派，以保证服务能顺利进行。"
    -   **核心流程与界面元素**:
        1.  在`待接单`的订单详情页，如果系统长时间未自动派单，会出现`手动派单`按钮。在已分配厨师的订单详情页，有`改派`按钮。
        2.  点击后，弹出一个侧边栏或模态框，里面是一个推荐的厨师列表。
        3.  **智能推荐**: 系统应根据订单的服务地址、菜系要求和时间，智能推荐最合适的厨师，并按推荐度排序。列表需展示厨师的姓名、距离、好评率等关键决策信息。
        4.  调度员选择一位厨师后，点击`确认指派`。
    -   **业务规则**: 系统推荐的厨师必须满足：菜系匹配、在该时间段内可服务、服务范围覆盖订单地址。
-   **FR12.4 - 退款/售后处理**:
    -   **用户故事**: "作为一名售后专员，我需要一个专门的队列来管理所有客户的退款和售后请求。我必须能看到客户的理由，审查订单详情，然后根据公司政策批准或驳回请求。"
    -   **核心流程与界面元素**:
        1.  一个专门的`售后中心`页面，以队列形式展示所有状态为`待处理`的退款/售后申请。
        2.  进入详情页后，可以清晰地看到用户的`申请理由`、上传的`图片证据`（如果有），以及完整的原始订单信息。
        3.  页面底部有明确的操作按钮：`批准退款`、`驳回申请`、`联系客户`。
        4.  如果选择`批准退款`，系统需允许专员输入`退款金额`（全额或部分）。确认后，该申请流转至财务的`待退款`队列。
    -   **业务规则**: 系统应根据平台的退款政策和订单的取消时间，自动计算并建议退款金额。所有处理步骤和理由都必须被记录。
-   **FR12.5 - "主题咨询"线索管理**:
    -   **用户故事**: "作为一名高端宴会销售，我需要一个简单的CRM系统来跟进所有从'私人定制'入口进来的咨询线索。我要能分配线索给团队成员，追踪每条线索的状态，并记录沟通笔记。"
    -   **核心流程与界面元素**:
        1.  一个独立的、区别于普通订单的列表，用于展示所有销售线索。
        2.  **列**: 线索ID、客户姓名、联系电话、预算范围、宴会日期、当前状态（`新线索`、`跟进中`、`已报价`、`已转化`、`已流失`）、负责人。
        4.  在线索详情页，提供一个`沟通记录`的文本区域，销售可以随时添加新的跟进笔记。
    -   **业务规则**: 这部分是销售线索，而非订单。在销售团队与客户确认并在线下签约前，它们不进入订单或财务系统。
##### 3.3.3. 内容与产品管理模块 (Content & Product Management)
-   **FR13.1 - 套餐管理**:
    -   **用户故事**: "作为一名内容经理，我需要能够创建官方的'精选套餐'用于推广活动，同时我也需要一个清晰的队列来审核厨师们提交的个人套餐，确保它们的质量和描述都符合平台标准。"
    -   **核心流程与界面元素**:
        1.  一个套餐管理页面，包含两个主要Tab: `平台套餐` 和 `厨师套餐审核`。
        2.  **平台套餐Tab**:
            -   一个数据网格，列出所有由官方创建的套餐。
            -   提供`创建新套餐`、`编辑`、`上架/下架`等CRUD功能。
            -   创建/编辑表单类似于厨师端，但可能包含更多运营选项（如设置推荐权重）。
        3.  **厨师套餐审核Tab**:
            -   一个专门的审核队列，展示所有状态为`待审核`的厨师套餐。
            -   审核流程与`FR11.2 - 厨师入驻审核`类似，审核员可以查看套餐详情，并`批准`或`驳回`。
    -   **业务规则**: 平台套餐由官方定价和管理。厨师套餐的价格和内容由厨师定义，但平台拥有最终审核权。
-   **FR13.2 - 服务模式管理**:
    -   **用户故事**: "作为一名业务运营经理，我需要能方便地调整我们核心服务模式的定价。比如，我想把'一口价加工'的基础服务费从199元调整到219元，或者修改额外加菜的费用。"
    -   **核心流程与界面元素**:
        -   一个配置页面，分块展示不同的服务模式。
        -   **一口价加工模块**: 提供输入框，用于设置`基础服务费`（按不同菜品数组合，如四菜一汤、六菜一汤）和`单菜加价费`。
        -   **私人定制模块**: 提供输入框，用于设置`按人均收费`的标准和`超时服务费`规则。
    -   **业务规则**: 此处修改的任何价格，将实时影响用户端新订单的计价。
-   **FR13.3 - 首页内容配置**:
    -   **用户故事**: "作为一名市场经理，我需要一个灵活的工具来随时更新App首页的内容，比如替换节假日的宣传海报，或者把本周最受欢迎的厨师推到首页推荐位。"
    -   **核心流程与界面元素**:
        -   一个可视化的仪表盘式界面，模拟手机首页的布局。
        -   **轮播图管理**: 支持上传图片、调整顺序、配置每张图的跳转链接。
        -   **服务入口管理**: 允许调整金刚区图标的顺序或替换图标。
        -   **推荐厨师/套餐管理**:
            -   提供一个`选择`按钮，点击后弹出厨师/套餐列表。
            -   运营人员可以从中勾选希望展示在首页的项目，并能拖拽排序。
    -   **业务规则**: 所有配置修改后，需要有一个`发布`按钮来使其在用户端生效。
-   **FR13.4 - 评价管理**:
    -   **用户故事**: "作为一名运营，我需要能够查看所有用户评价，以监控质量并发现问题。如果我发现违反我们条款的不当或恶意评价，我必须能够将其从公众视野中隐藏。"
    -   **核心流程与界面元素**:
        -   一个列出所有评价的数据网格。
        -   **列**: 评价ID、订单ID、客户、厨师、评分、评论、时间戳。
        -   **筛选**: 能够按评分（如显示所有一星评价）、按厨师或客户搜索。
        -   每行都有一个`隐藏`/`显示`的切换按钮。
    -   **业务规则**: 被隐藏的评价在用户端和厨师端应用中都不可见，但会保留在数据库中以供记录。原作者不会被通知其评价被隐藏。且不会被记入评分
##### 3.3.4. 财务管理模块 (Financial Management)
-   **FR14.1 - 提现申请管理**:
    -   **用户故事**: "作为一名财务专员，我需要一个安全的队列来查看所有厨师**和推广员**的提现请求。我必须能够核对请求的详细信息与我们的内部记录，在线下完成实际的银行转账，然后在系统中将请求标记为'已完成'或'失败'，以保持准确的交易日志。"
    -   **核心流程与界面元素**:
        -   一个专门的页面，显示所有提现请求的列表，并带有状态（`待处理`、`已处理`、`已拒绝`）。
        -   **列**: 请求ID、申请人姓名、**用户类型 (厨师/推广员)**、申请金额、银行账户信息、申请日期、状态。
        -   财务专员可以点击进入一个请求查看详情。
        -   在线下完成银行转账后，他们在系统中将状态更改为`已处理`。如果转账失败，他们将其标记为`已拒绝`，并必须提供原因。
    -   **业务规则**: 这是一个两步过程。系统中的操作（标记为完成）是在真实世界的操作（银行转账）之后进行的。此模块需要最高级别的安全性和访问控制。
##### 3.3.5. 推广员管理模块 (Promoter Management)
-   **FR15.1 - 推广员申请审核**:
    -   **用户故事**: "作为运营人员，我需要审核希望成为推广员的用户申请，以确保其信息的有效性，并筛选出高质量的合作伙伴。"
    -   **核心流程与界面元素**:
        -   一个专门的审核队列，列出所有`待审核`的推广员申请。
        -   点击申请后，可以查看申请人提交的`联系人姓名`、`联系电话`、`邮箱`和`形象照`。
        -   页面底部有`批准`和`驳回`的操作按钮。`驳回`时需要填写理由。
    -   **业务规则**: 审核通过后，该用户账户将被赋予"推广员"身份，并可以访问用户端的推广员中心。
-   **FR15.2 - 推广员列表与查询**:
    -   **用户故事**: "我需要一个推广员列表，可以查看所有推广员的表现，比如他们邀请了多少人，创造了多少收益，方便我管理和评估推广效果。"
    -   **核心流程与界面元素**:
        -   一个数据网格，列出所有已批准的推广员。
        -   **列**: 推广员ID、姓名、电话、邀请用户数、关联订单数、累计佣金、当前状态（`活跃`/`暂停`）。
        -   支持按姓名/电话搜索，并提供`查看详情`、`暂停资格`等操作。
    -   **业务规则**: `暂停资格`后，该推广员的推广链接将失效，但不会影响已产生的佣金。
-   **FR15.3 - 推广规则配置**:
    -   **用户故事**: "我需要能灵活配置推广员的佣金比例，可能不同地区的市场政策不一样，或者针对特定活动调整比例。"
    -   **核心流程与界面元素**:
        -   一个配置页面，可以设置`默认佣金比例`（例如3%）。
        -   支持按地区或其他维度（如推广员等级）设置特殊的佣金比例。
    -   **业务规则**: 规则的变动只影响未来新产生的订单佣金。
-   **FR15.4 - 推广员中心**:
    -   **用户故事**: "作为推广员，我希望有一个中心来查看我的推广效果和财务状况。"
    -   **核心流程与界面元素**:
        1.  推广员中心页面，展示推广员的基本信息和推广数据。
        2.  提供一个`查看详情`按钮，点击后可以查看更多信息。
        3.  **推广员中心 (审核通过后可见)**:
            -   入口替换原来的`成为推广员`。
            -   **推广物料**: 页面顶部显著位置提供`我的专属推广码`和一键复制`推广链接`的按钮。
            -   **业绩与财务看板**:
                -   展示`累计邀请人数`、`累计订单数`、`累计佣金`和`可提现佣金`。
                -   提供一个醒目的`提现`按钮。点击后，用户进入提现流程。首次提现需绑定银行账户，然后输入金额提交申请。
            -   **明细与记录**:
                -   提供`邀请记录`、`佣金记录`、`提现记录`三个Tab。
                -   `邀请记录`展示所有通过其链接注册的用户。
                -   `佣金记录`展示每一笔佣金的来源订单和金额。
                -   `提现记录`展示所有提现申请及其处理状态（`处理中`、`已到账`、`失败`）。
    -   **业务规则**:
        -   申请需后台审核。审核通过后，用户账户即获得推广员身份。
        -   推广员的佣金结算和提现规则（如结算周期、最低提现额）与厨师保持一致，由后台财务模块统一管理。
