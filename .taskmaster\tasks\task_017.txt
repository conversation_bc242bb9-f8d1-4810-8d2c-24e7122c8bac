# Task ID: 17
# Title: “精选套餐”模式列表与详情
# Status: pending
# Dependencies: 8, 10, 11, 13
# Priority: high
# Description: 开发“精选套餐”服务模式的套餐列表页和套餐详情页。套餐列表以精美图文卡片形式展示，详情页包含套餐主图、名称、价格、菜品清单和服务说明，并接入通用预约流程。
# Details:
套餐列表使用`u-grid`或`u-list`展示卡片。套餐详情页使用`u-swiper`展示主图，菜品清单可使用`u-collapse`或`u-list`分组展示。点击“立即预订”跳转到通用预约页。

# Test Strategy:
测试套餐列表和详情页的数据加载和展示是否正常；验证点击“立即预订”后，是否能正确跳转到预约页并带入套餐信息。

# Subtasks:
## 1. Define Package Data Model & API Endpoints [pending]
### Dependencies: None
### Description: Design the backend data structure for featured packages (e.g., name, description, price, images, services included) and define the necessary API endpoints for fetching package lists and individual package details.
### Details:
This involves collaboration with backend team to finalize schema and RESTful endpoints.

## 2. Develop Featured Package List Page UI [pending]
### Dependencies: 17.1
### Description: Create the front-end UI for displaying a list of featured packages, including package name, price, and a placeholder for a thumbnail image. Implement initial data fetching from the defined API endpoint.
### Details:
Focus on responsive layout and basic information display for each package card.

## 3. Implement Package List Image Handling [pending]
### Dependencies: 17.2
### Description: Develop the logic for efficiently loading, displaying, and caching thumbnail images for packages on the list page, ensuring responsiveness and performance.
### Details:
Consider image optimization techniques (e.g., lazy loading, responsive images) and error handling for missing images.

## 4. Develop Featured Package Detail Page UI [pending]
### Dependencies: 17.1, 17.2
### Description: Design and implement the front-end UI for the individual package detail page, displaying comprehensive information such as full description, included services, menu details, and pricing options. Implement data fetching for a single package.
### Details:
Ensure all relevant package attributes are displayed clearly and attractively. Include sections for 'What's Included', 'Menu', 'Pricing Options'.

## 5. Implement Package Detail Image Gallery [pending]
### Dependencies: 17.4
### Description: Develop a robust image gallery or carousel component for the package detail page to showcase multiple high-resolution images associated with the package.
### Details:
Include features like full-screen view, navigation controls, and image captions.

## 6. Integrate Universal Booking with Package Detail [pending]
### Dependencies: 17.4
### Description: Connect the package detail page with the existing universal booking process, allowing users to select a package, choose options (e.g., date, time, number of guests), and proceed to booking directly from the detail page.
### Details:
This involves passing selected package ID and options to the booking flow, ensuring a seamless user experience.

