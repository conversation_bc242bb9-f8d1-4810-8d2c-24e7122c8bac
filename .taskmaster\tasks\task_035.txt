# Task ID: 35
# Title: 厨师订单历史与操作
# Status: pending
# Dependencies: 34
# Priority: high
# Description: 开发厨师订单历史页面，提供Tab式导航按历史状态筛选（全部、已完成、已评价、已取消），并支持按月份进行筛选或搜索。在订单详情页实现“联系客户”和“确认完成”操作。
# Details:
订单历史页使用`u-tabs`和`u-list`。订单详情页的“联系客户”按钮可拉起电话拨号或通过平台客服转接。“确认完成”按钮点击后，更新订单状态为`待评价`。

# Test Strategy:
测试订单历史列表的筛选和搜索功能；验证“联系客户”功能是否正常；检查“确认完成”操作后，订单状态是否正确更新并触发结算流程。

# Subtasks:
## 1. Backend API & Database Schema for Orders [pending]
### Dependencies: None
### Description: Design and implement the database schema for orders, including status, customer info, chef info, and service details. Develop API endpoints for fetching order lists and individual order details.
### Details:
This foundational step ensures data availability for both customer and chef interfaces.

## 2. Develop Order History Page (Customer View) [pending]
### Dependencies: 35.1
### Description: Create the front-end UI for the customer's order history page, displaying a list of their past and current orders. Focus on basic layout and data display.
### Details:
This involves fetching and rendering order data from the backend API.

## 3. Implement Filtering for Order History Page [pending]
### Dependencies: 35.2
### Description: Add tabbed filtering by order status (e.g., 'Pending', 'Completed', 'Cancelled') and date range/month filtering to the customer's order history page.
### Details:
This enhances usability by allowing customers to easily navigate their order history.

## 4. Develop Chef's Order Detail Page [pending]
### Dependencies: 35.1
### Description: Create the front-end UI for the chef's detailed view of a specific order, displaying all relevant order information, customer details, and service specifics.
### Details:
This page is crucial for chefs to manage and understand individual orders.

## 5. Implement 'Contact Customer' Functionality [pending]
### Dependencies: 35.4
### Description: Add buttons/links on the chef's order detail page to initiate contact with the customer via phone call and/or in-app chat.
### Details:
This enables direct communication between chefs and customers for order-related queries.

## 6. Implement 'Confirm Completion' Action [pending]
### Dependencies: 35.4
### Description: Develop the functionality on the chef's order detail page for the chef to mark an order as 'Completed', including updating the order status via API.
### Details:
This action is critical for updating order status and triggering subsequent processes like payment or reviews.

