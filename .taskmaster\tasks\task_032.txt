# Task ID: 32
# Title: 厨师实名认证集成
# Status: pending
# Dependencies: 30
# Priority: high
# Description: 集成第三方实名认证服务（如腾讯云E-KYC），实现厨师的人脸识别和身份证号码验证流程。在厨师端界面上更新显示“已认证”徽章。
# Details:
在资料完善流程中引导厨师进行实名认证。调用微信小程序提供的第三方认证接口或跳转到认证小程序。后端接收认证结果并更新厨师的`is_verified`状态。

# Test Strategy:
测试实名认证流程是否能正确跳转和返回结果；验证认证成功后，厨师端是否显示“已认证”徽章；检查认证失败时的重试机制。

# Subtasks:
## 1. Configure Third-Party KYC Service Integration [pending]
### Dependencies: None
### Description: Set up API keys, credentials, and integrate the Tencent Cloud E-KYC SDK/API into both frontend and backend environments. This includes initial configuration, network setup, and ensuring secure communication channels.
### Details:
Establish secure connection, configure authentication, and initialize SDKs.

## 2. Implement Client-Side Face Recognition & ID Verification Flow [pending]
### Dependencies: 32.1
### Description: Develop the user interface and logic for initiating the KYC process, guiding the user through face recognition and ID document scanning using the integrated third-party service's client-side components.
### Details:
Design user flow, integrate camera access, handle real-time feedback from KYC service.

## 3. Develop Backend Endpoint for KYC Result Handling [pending]
### Dependencies: 32.1
### Description: Create a secure backend endpoint to receive and process the authentication results (success/failure, verification data) from the third-party KYC service, including handling webhooks or polling for status updates.
### Details:
Implement callback listener, parse verification data, validate authenticity of results.

## 4. Update Frontend UI with Verification Status [pending]
### Dependencies: 32.3
### Description: Implement logic to update the user interface, specifically displaying a 'verified' badge or appropriate success/failure messages, based on the authentication results received from the backend.
### Details:
Display 'verified' badge, show error messages for failures, provide user feedback.

## 5. Securely Store Verification Status & Audit Logs [pending]
### Dependencies: 32.3
### Description: Persist the final KYC verification status (e.g., 'verified', 'failed', 'pending') in the database for the user, and implement comprehensive logging for audit trails and compliance purposes.
### Details:
Update user profile with verification status, log all KYC attempts and outcomes, ensure data privacy compliance.

