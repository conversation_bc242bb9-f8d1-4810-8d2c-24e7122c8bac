# Task ID: 13
# Title: 通用订单确认与支付流程
# Status: pending
# Dependencies: 4, 10, 11, 12
# Priority: high
# Description: 开发通用的订单确认页面，汇总展示所有订单信息（服务模式、时间、地址、费用明细）。集成微信支付功能，实现用户通过微信支付完成订单。开发支付成功和失败的提示页面。
# Details:
订单确认页使用`u-cell-group`和自定义布局展示各项信息。支付按钮点击后调用`uni.requestPayment`拉起微信支付。支付结果通过后端回调确认，前端跳转到对应的结果页。

# Test Strategy:
测试订单信息汇总是否准确；验证微信支付流程是否顺畅，支付成功和失败的页面跳转是否正确；检查支付状态与后端同步是否一致。

# Subtasks:
## 1. Develop Order Confirmation Page Frontend [pending]
### Dependencies: None
### Description: Design and implement the user interface for the order confirmation page, displaying order details, total amount, and a 'Proceed to Pay' button.
### Details:
Includes UI/UX design, data binding for order details, and basic page navigation.

## 2. Implement Backend Payment Initiation & Callback Endpoints [pending]
### Dependencies: None
### Description: Develop backend APIs to create a payment order, generate WeChat Pay parameters (e.g., prepay_id), and set up a secure callback endpoint for WeChat to notify payment status.
### Details:
Involves secure API key management, signature generation, and initial database schema for payment records.

## 3. Integrate WeChat Pay (`uni.requestPayment`) on Frontend [pending]
### Dependencies: 13.1, 13.2
### Description: Implement the frontend logic to call the backend payment initiation API (Subtask 2) and then invoke `uni.requestPayment` with the received parameters.
### Details:
Handle network requests, parameter parsing, and error handling for the payment initiation call.

## 4. Develop Frontend Payment Result Handling Logic [pending]
### Dependencies: 13.3
### Description: Implement the logic to process the result of `uni.requestPayment` (success/fail/cancel) and redirect the user to the appropriate dedicated success or failure page.
### Details:
Includes parsing the `uni.requestPayment` response, conditional redirection, and basic error logging.

## 5. Develop Dedicated Payment Success Page [pending]
### Dependencies: 13.4
### Description: Create a dedicated frontend page to display a successful payment confirmation, including order number and next steps (e.g., view order history).
### Details:
Focus on clear success messaging, user-friendly design, and relevant post-payment actions.

## 6. Develop Dedicated Payment Failure Page [pending]
### Dependencies: 13.4
### Description: Create a dedicated frontend page to inform the user about a payment failure, providing reasons (if available) and options to retry or contact support.
### Details:
Focus on clear failure messaging, troubleshooting tips, and options for re-attempting payment or seeking help.

