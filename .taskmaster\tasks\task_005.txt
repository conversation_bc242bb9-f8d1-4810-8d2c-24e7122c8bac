# Task ID: 5
# Title: 微信一键登录与隐私协议功能
# Status: done
# Dependencies: 4
# Priority: high
# Description: 实现微信一键登录功能，通过调用微信官方接口获取用户手机号进行注册或登录。开发隐私协议和服务协议的弹窗和详情页，确保用户在授权前必须同意相关协议。
# Details:
使用`uni.login`获取code，然后调用后端接口换取用户手机号和OpenID。前端根据后端返回的登录状态（新用户/老用户）进行相应处理。隐私协议弹窗使用`u-modal`或自定义组件，协议内容通过`u-parse`或`web-view`展示。

# Test Strategy:
测试新用户和老用户的登录流程是否顺畅；验证用户拒绝授权时的友好提示；检查隐私协议和服务协议链接是否可点击，内容是否完整展示；确保未勾选协议时登录按钮不可用。

# Subtasks:
## 1. WeChat Developer Account Setup & API Key Acquisition [done]
### Dependencies: None
### Description: Register a WeChat developer account, create an application, and obtain the App ID and App Secret required for API integration and configuration.
### Details:
This involves navigating the WeChat Open Platform, submitting necessary documentation, and configuring the application for web or mobile login.

## 2. Frontend WeChat SDK Integration [done]
### Dependencies: 5.1
### Description: Integrate the appropriate WeChat SDK (e.g., JS-SDK for web, or relevant mobile SDK for iOS/Android) into the client-side application to enable WeChat login functionality.
### Details:
Configure the SDK with the obtained App ID and ensure proper initialization and readiness for API calls.

## 3. Frontend WeChat Login Button & Authorization Flow Implementation [done]
### Dependencies: 5.2
### Description: Develop the UI for the 'Login with WeChat' button and implement the client-side logic to initiate the WeChat authorization flow, capturing the authorization code upon successful user authentication.
### Details:
This includes handling redirects, pop-ups, and securely receiving the authorization code from WeChat's callback.

## 4. Backend API for WeChat Authentication & User Management [done]
### Dependencies: 5.3
### Description: Create backend endpoints to receive the WeChat authorization code, exchange it for access tokens and user information, and handle user registration (if new) or login (if existing) in the application's database.
### Details:
Implement secure token exchange, user data fetching, and mapping WeChat user IDs to internal user accounts, including session management.

## 5. Privacy Agreement Modal/Page Development [done]
### Dependencies: None
### Description: Design and implement the user interface for the privacy protocol agreement, presented as a modal or dedicated page, detailing data usage, user rights, and consent options.
### Details:
Ensure the UI is clear, legally compliant, and provides explicit options for users to accept or decline the terms.

## 6. User Consent Handling & Enforcement Logic [done]
### Dependencies: 5.4, 5.5
### Description: Implement the backend and frontend logic to record user consent to the privacy agreement, store it securely, and enforce application behavior based on the user's consent status (e.g., requiring consent before proceeding with login or data access).
### Details:
This involves updating user profiles with consent status, auditing consent changes, and conditionally enabling features based on consent.

