# Task ID: 33
# Title: 厨师个人套餐创建与管理
# Status: pending
# Dependencies: 31
# Priority: high
# Description: 开发厨师创建、编辑、上架/下架、删除个人套餐的功能。套餐创建表单包含套餐主图、名称、价格、适用人数、菜品数量和动态菜品清单管理。
# Details:
套餐列表页展示所有套餐卡片，提供操作按钮。创建/编辑页使用`u-form`构建表单，菜品清单使用可动态增删的列表组件。套餐提交后状态为`待审核`。

# Test Strategy:
测试套餐的增删改查功能是否正常；验证套餐上架/下架状态切换；检查提交审核后，状态是否正确更新。

# Subtasks:
## 1. Develop Backend API for Chef Package Management [pending]
### Dependencies: None
### Description: Design and implement RESTful APIs for full CRUD operations on chef packages, including nested dish lists, image storage integration, and package status updates (on/off shelf, pending review).
### Details:
Define data models for Package, Dish, and Image. Implement endpoints for creating, reading, updating, and deleting packages. Include endpoints for managing dishes within a package and handling image uploads/retrieval.

## 2. Implement Chef Package List Display [pending]
### Dependencies: 33.1
### Description: Create the user interface to display a list of all chef packages with key details (name, status, number of dishes, main image). Include filtering and sorting capabilities.
### Details:
Develop frontend components to fetch and render package data from the backend API. Design the layout for package cards/rows.

## 3. Build Create/Edit Chef Package Form [pending]
### Dependencies: 33.1
### Description: Develop a comprehensive form for chefs to create new packages or edit existing ones. This form will include fields for package name, description, price, and placeholders for dynamic dish lists and image uploads.
### Details:
Design form fields, validation rules, and submission logic. Ensure the form can pre-populate data for editing existing packages.

## 4. Integrate Dynamic Dish List Management into Form [pending]
### Dependencies: 33.1, 33.3
### Description: Implement the functionality within the Create/Edit Package Form to dynamically add, edit, reorder, and remove dishes associated with a package. Each dish should have fields like name, description, and price.
### Details:
Develop UI components for managing dish items (e.g., 'Add Dish' button, editable rows, drag-and-drop reordering). Ensure data is correctly structured for backend submission.

## 5. Implement Image Upload Functionality [pending]
### Dependencies: 33.1, 33.3
### Description: Integrate image upload capabilities into the Create/Edit Package Form, allowing chefs to upload a main image for their package. This includes client-side validation and secure storage integration.
### Details:
Develop UI for image selection and preview. Implement logic for uploading images to the backend and associating them with the correct package. Handle image resizing/optimization if necessary.

## 6. Develop Chef Package Status Management [pending]
### Dependencies: 33.1, 33.2
### Description: Implement the UI and logic for managing package statuses (e.g., 'On Shelf', 'Off Shelf', 'Pending Review'). This includes displaying the current status and providing controls for chefs to change it where applicable.
### Details:
Add status indicators to the package list and/or detail view. Implement buttons/dropdowns for status changes, ensuring proper permissions and backend calls.

